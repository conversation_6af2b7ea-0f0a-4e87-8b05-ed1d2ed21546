<template>
  <j-crud
    v-if="init && tabs.length > 0"
    ref="crud"
    v-model:checked-row-keys="checkedKeys"
    :del-method="deleteAmsProperty"
    :query-form="queryForm"
    :dataTableStriped="true"
    :query-method="queryAmsProperty"
    :show-operation-button="showMenu && showOperation"
    :tabs="NotFixedAssetMode ? tabs.slice(0, 2) : tabs"
    default-check-tab="1"
    show-export
    :sign-line="false"
    :virtual-scroll="false"
    @query-complete="queryComplete"
    :exportConfig="exportConfig"
    :quick-query-config="{ enable: true, excludeKey: ['asset'] }"
    @query-after="
      () => {
        advancedFilteringApplyAndQuery(false)
      }
    "
  >
    <template #extendFormItems>
      <n-form-item label="资产" path="asset">
        <n-input v-model:value="queryForm.asset" clearable placeholder="请输入资产编码或名称" />
      </n-form-item>
      <n-form-item label="资产类型" path="assetStatus">
        <n-tree-select
          v-model:value="queryForm.assetType"
          :options="amsDict.dict['资产类型']"
          :placeholder="'请选择'"
          clearable
          filterable
          key-field="assetTypeCode"
          label-field="assetTypeName"
        ></n-tree-select>
      </n-form-item>
      <n-form-item label="资产类型(新分类)">
        <n-tree-select
          v-model:value="queryForm.assetTypeN"
          :options="amsDict.dict['资产类型(新分类)']"
          :placeholder="'请选择'"
          clearable
          filterable
          key-field="assetTypeCode"
          label-field="assetTypeName"
        ></n-tree-select>
      </n-form-item>
      <n-form-item label="入库时间">
        <n-date-picker
          v-model:formatted-value="queryForm.inptDateRange"
          :actions="['clear', 'confirm']"
          :clearable="true"
          :shortcuts="rangeShortcuts"
          :update-value-on-close="true"
          format="yyyyMMdd"
          style="width: 100%"
          type="daterange"
          value-format="yyyyMMdd"
        />
      </n-form-item>
      <n-form-item>
        <template #label>
          <n-popselect v-model:value="queryForm.deptField" :options="deptOptions">
            {{ deptLabel }}
          </n-popselect>
        </template>
        <n-tree-select
          v-if="queryForm.deptField === 'storage_area'"
          v-model:value="queryForm.deptCode"
          :options="amsDict.dict['storageArea']"
          checkStrategy="all"
          clearable
          filterable
          key-field="storageAreaCode"
          label-field="storageArea"
          @queryComplete="queryComplete"
        />
        <j-bus-hos-org v-else v-model:value="queryForm.deptCode" checkStrategy="all" clearable />
      </n-form-item>
    </template>
    <template #extendButtons>
      <n-popconfirm @positive-click="fnToChk" v-if="showChk && showMenu">
        <template #trigger>
          <n-button strong :disabled="!(checkedKeys.length > 0)" type="info">审核</n-button>
        </template>
        确定要审核吗？
      </n-popconfirm>

      <n-popconfirm @positive-click="fnUnToChk" v-if="showUnToChk && showMenu">
        <template #trigger>
          <n-button :disabled="!(checkedKeys.length > 0)" strong type="warning">销审</n-button>
        </template>
        确定要销审吗？
      </n-popconfirm>
      <n-button v-if="showPrintBtn && showMenu" type="primary" @click="printLabel">标签打印</n-button>
      <n-button type="info" secondary @click="queryHistoryData">
        查询历史数据<span v-if="queryForm.queryHistoryDataMonth">({{ queryForm.queryHistoryDataMonth }})</span>
      </n-button>

      <n-button
        v-show="tableName == '1' || tableName == '0'"
        secondary
        @click="switchSummary"
        strong
        warning
        type="primary"
        >合计</n-button
      >
      <el-badge
        :value="getActiveFiltersCount() > 0 && !showFilterPanel ? getActiveFiltersCount() : 0"
        :show-zero="false"
        :offset="[-3, 8]"
      >
        <n-button  @click="switchFilter" strong  color="#8a2be2">高级筛选</n-button>
      </el-badge>
    </template>
    <template #contentTop>
      <!-- 合计汇总面板 -->
      <n-spin :show="showSummaryLoding" v-show="showSummary">
        <template #description> 合计汇总中... </template>
        <TempComponent :is="tabContentRender" />
      </n-spin>

      <!-- 资产筛选面板 -->
      <div v-show="showFilterPanel" class="filter-panel-container">
        <div class="filter-panel-header">
          <div class="header-left">
            <n-icon size="18" style="margin-right: 8px; color: #18a058">
              <FilterOutline />
            </n-icon>
            <span class="panel-title">资产高级筛选</span>
            <n-badge :value="getActiveFiltersCount()" :show-zero="false">
              <span class="condition-count">已设置条件</span>
            </n-badge>
          </div>
          <div class="header-right">
            <n-button size="small" type="info" secondary @click="showFilterDoc = true" style="margin-right: 8px">
              <template #icon>
                <n-icon size="14">
                  <DocumentTextOutline />
                </n-icon>
              </template>
              使用说明
            </n-button>
            <n-button
              v-if="getActiveFiltersCount() > 0"
              size="small"
              type="warning"
              secondary
              @click="clearAllFilters"
              style="margin-right: 8px"
            >
              清空全部
            </n-button>
            <n-button size="small" type="primary" @click="advancedFilteringApplyAndQuery" :disabled="getActiveFiltersCount() === 0">
              <template #icon>
                <n-icon size="14">
                  <SearchOutline />
                </n-icon>
              </template>
              应用筛选
            </n-button>
            <n-button size="small" type="info" @click="closeFilterPanel"> 收起 </n-button>
          </div>
        </div>

        <div class="filter-panel-content">
          <!-- 左侧：字段选择区域 -->
          <div class="filter-fields-section">
            <div class="section-header">
              <span class="section-title"
                >可筛选字段 （{{ propertyData.filter((item: any) => !item.searchType).length }}）</span
              >
            </div>
            <div class="fields-table-container">
              <j-n-data-table
                ref="tableRef"
                :columns="screenColumn"
                :data="propertyData"
                :pagination="false"
                size="small"
                :bordered="false"
                :single-line="false"
                max-height="400"
                virtual-scroll
                style="--n-td-padding: 6px 8px"
              />
            </div>
          </div>

          <!-- 右侧：已选条件展示区域 -->
          <div class="filter-conditions-section">
            <div class="section-header">
              <span class="section-title">已选择条件</span>
              <n-tag v-if="getActiveFiltersCount() > 0" type="success" size="small">
                {{ getActiveFiltersCount() }} 个条件
              </n-tag>
            </div>

            <!-- 筛选条件为空时的提示 -->
            <div v-if="getActiveFiltersCount() === 0" class="empty-conditions">
              <n-empty description="暂无筛选条件" size="small">
                <template #icon>
                  <n-icon size="32" style="color: #d0d0d0">
                    <FilterCircleOutline />
                  </n-icon>
                </template>
                <template #extra>
                  <span style="color: #999; font-size: 12px">请在左侧选择需要筛选的字段</span>
                </template>
              </n-empty>
            </div>

            <!-- 筛选条件卡片展示 -->
            <div v-else class="conditions-container">
              <div class="conditions-grid">
                <transition-group name="filter-card" tag="div" class="filter-cards-container">
                  <div v-for="(item, index) in getActiveFilters()" :key="item.key" class="filter-condition-card">
                    <!-- 卡片头部 -->
                    <div class="card-header">
                      <div class="field-info">
                        <n-icon size="12" :style="{ color: getFieldColor(item.type), marginRight: '4px' }">
                          <component :is="getFieldIcon(item.type)" />
                        </n-icon>
                        <span class="field-name" @click="scroll(getOriginalIndex(item))" title="点击定位到字段">
                          {{ item.title }}
                        </span>
                      </div>
                      <n-button size="tiny" type="error" secondary circle @click="fnClear(item)" title="删除此条件">
                        X
                      </n-button>
                    </div>

                    <!-- 筛选类型选择 -->
                    <div class="filter-type-section">
                      <span class="filter-type-label">筛选方式：</span>
                      <component :is="item.getNodeFnselectType(100)" class="filter-type-select"></component>
                    </div>

                    <!-- 筛选值输入 -->
                    <div class="filter-value-section">
                      <div class="filter-value-input">
                        <!-- 下拉选择类型 -->
                        <j-select
                          v-if="item.type === 'select'"
                          v-model:value="item.searchData"
                          :dictType="(item as any).dictType"
                          :disabled="item.searchType == '3'"
                          :options="item.options ? item.options : amsDict.dict[item.optionKey]"
                          placeholder="请选择"
                          clearable
                          size="small"
                        />
                        <!-- 树形下拉选类型 -->
                        <n-tree-select
                          v-else-if="item.type === 'tree_select'"
                          v-model:value="item.searchData"
                          :key-field="item.treeProp ? item.treeProp.key : 'key'"
                          :label-field="item.treeProp ? (item.treeProp as any).label : 'label'"
                          :multiple="false"
                          :options="item.options ? item.options : amsDict.dict[item.optionKey]"
                          :disabled="item.searchType == '3'"
                          filterable
                          placeholder="请选择"
                          clearable
                          size="small"
                        />
                        <!-- 日期筛选输入 -->
                        <div v-else-if="item.type === 'date'" class="date-filter-container">
                          <n-select
                            v-model:value="item.dateOperator"
                            :options="dateOperatorOptions"
                            placeholder="选择条件"
                            size="small"
                            style="width: 120px; margin-right: 8px"
                            @update:value="updateDateFilter(item)"
                          />
                          <n-date-picker
                            v-if="item.dateOperator !== 'between'"
                            v-model:formatted-value="item.dateValue"
                            :actions="['clear', 'confirm']"
                            :clearable="true"
                            :disabled="item.searchType == '3'"
                            type="date"
                            value-format="yyyyMMdd"
                            placeholder="请选择日期"
                            size="small"
                            style="flex: 1"
                            @update:formatted-value="updateDateFilter(item)"
                          />
                          <div v-else style="display: flex; align-items: center; flex: 1">
                            <n-date-picker
                              v-model:formatted-value="item.dateMin"
                              :actions="['clear', 'confirm']"
                              :clearable="true"
                              :disabled="item.searchType == '3'"
                              type="date"
                              value-format="yyyyMMdd"
                              placeholder="开始日期"
                              size="small"
                              style="flex: 1"
                              @update:formatted-value="updateDateFilter(item)"
                            />
                            <span style="margin: 0 8px; color: #999">至</span>
                            <n-date-picker
                              v-model:formatted-value="item.dateMax"
                              :actions="['clear', 'confirm']"
                              :clearable="true"
                              :disabled="item.searchType == '3'"
                              type="date"
                              value-format="yyyyMMdd"
                              placeholder="结束日期"
                              size="small"
                              style="flex: 1"
                              @update:formatted-value="updateDateFilter(item)"
                            />
                          </div>
                        </div>
                        <!-- 数值筛选输入 -->
                        <div v-else-if="item.type === 'number'" class="number-filter-container">
                          <n-select
                            v-model:value="item.numberOperator"
                            :options="numberOperatorOptions"
                            placeholder="选择条件"
                            size="small"
                            style="width: 120px; margin-right: 8px"
                            @update:value="updateNumberFilter(item)"
                          />
                          <n-input-number
                            v-if="item.numberOperator !== 'between'"
                            v-model:value="item.numberValue"
                            :disabled="item.searchType == '3'"
                            placeholder="请输入数值"
                            size="small"
                            :show-button="false"
                            style="flex: 1"
                            @update:value="updateNumberFilter(item)"
                          />
                          <div v-else style="display: flex; align-items: center; flex: 1">
                            <n-input-number
                              v-model:value="item.numberMin"
                              :disabled="item.searchType == '3'"
                              placeholder="最小值"
                              size="small"
                              :show-button="false"
                              style="flex: 1"
                              :max="item.numberMax"
                              @update:value="updateNumberFilter(item)"
                            />
                            <span style="margin: 0 8px; color: #999">至</span>
                            <n-input-number
                              v-model:value="item.numberMax"
                              :disabled="item.searchType == '3'"
                              placeholder="最大值"
                              size="small"
                              style="flex: 1"
                              :min="item.numberMin"
                              :show-button="false"
                              @update:value="updateNumberFilter(item)"
                            />
                          </div>
                        </div>
                        <!-- 组织机构选择 -->
                        <j-bus-hos-org
                          v-else-if="item.type === 'org'"
                          v-model:value="item.searchData"
                          :disabled="item.searchType == '3'"
                          check-strategy="all"
                        />
                        <!-- 文本输入框 -->
                        <n-input
                          v-else
                          v-model:value="item.searchData"
                          :disabled="item.searchType == '3'"
                          placeholder="请输入筛选内容"
                          clearable
                          size="small"
                        />
                      </div>
                    </div>

                    <!-- 条件预览 -->
                    <div v-if="false && getFilterPreview(item)" class="filter-preview">
                      <n-tag size="tiny" type="info" :bordered="false">
                        {{ getFilterPreview(item) }}
                      </n-tag>
                    </div>
                  </div>
                </transition-group>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <template #content>
      <n-modal v-model:show="showModal" :mask-closable="false" :style="orgFlowStyle" :z-index="10">
        <n-card :content-style="{ width: '100%', height: '100%', padding: '10px' }">
          <div style="height: 100%">
            <amsStock
              ref="amsStockRef"
              :style="{ height: stockDivHeight }"
              v-model:formData="modalData"
              :img-data="imgData"
              :is-edit="showMenu"
              :show-diff="true"
              :show-save="(tableName == '0' || tableName == '1') && showMenu && !queryForm.queryHistoryDataMonth"
              :show-type="false"
              @afterSave="saveData"
            >
              <template #titleLeft>
                <n-popover trigger="hover">
                  <template #trigger>
                    <j-icon
                      class="no-print"
                      :height="22"
                      :width="22"
                      name="back"
                      style="margin-right: 10px; cursor: pointer"
                      @click="() => (showModal = false)"
                    />
                  </template>
                  <span>返回</span>
                </n-popover>
              </template>
            </amsStock>

            <!-- <n-collapse
              v-model:expanded-names="expandedNames"
              :style="{ height: expandedNames.includes('details') ? '300px' : '0px' }"
            >
              <n-collapse-item name="details">
                <template #header> 详情记录 </template>
                <n-tabs>
                  <n-tab-pane name="变动记录" title="变动记录">
                    <j-n-data-table :columns="detailsTabs[0].columns" :data="changeData" max-height="220" />
                  </n-tab-pane>

                  <n-tab-pane name="资产子项" title="资产子项">
                    <j-n-data-table :columns="detailsTabs[1].columns" :data="sonData" max-height="220" />
                  </n-tab-pane>
                </n-tabs>
              </n-collapse-item>
            </n-collapse> -->
          </div>
        </n-card>
      </n-modal>
      <n-modal v-model:show="showPrint" :mask-closable="false" :style="orgFlowStyle" :z-index="10">
        <n-card
          :content-style="{ width: '100%', height: '100%' }"
          :on-close="() => (showPrint = false)"
          closable
          title="标签打印"
        >
          <template #header>
            <div style="display: flex; align-items: center">
              <div style="margin-left: 1rem">标签打印</div>
              <n-switch style="margin-left: 10px" checked-value="1" unchecked-value="2" v-model:value="netWork">
                <template #checked> 外网</template>
                <template #unchecked> 内网</template>
              </n-switch>
            </div>
          </template>
          <template #footer>
            <print-modal v-if="showPrint" :checked-keys="checkedKeys" :netWork="netWork" :printOptions="printOptions" />
          </template>
        </n-card>
      </n-modal>

      <!-- 新增历史数据月份选择弹窗 -->
      <n-modal
        v-model:show="showHistoryModal"
        preset="card"
        style="width: 400px"
        title="选择历史数据月份"
        :mask-closable="false"
        :z-index="10"
      >
        <n-form-item label="查询月份" label-placement="left">
          <n-date-picker
            v-model:formatted-value="selectedHistoryMonth"
            type="month"
            value-format="yyyyMM"
            clearable
            placeholder="请选择月份"
            style="width: 100%"
          />
        </n-form-item>
        <n-alert type="info"> 提示：历史数据存在开始时间为2024年12月 </n-alert>
        <template #footer>
          <n-space justify="end">
            <n-button @click="cancelHistorySelection">取消</n-button>
            <n-button type="primary" @click="confirmHistorySelection">确定</n-button>
          </n-space>
        </template>
      </n-modal>

      <!-- 筛选使用文档弹窗 -->
      <FilterDocModal v-model:show="showFilterDoc" />
    </template>
  </j-crud>
</template>
<script lang="ts">
  import { computed, defineComponent, h, nextTick, onBeforeMount, onMounted, reactive, ref, toRaw, watch } from 'vue'
  import { CRUDColumnInterface, JTab } from '@/types/comps/crud'
  import {
    deleteAmsProperty,
    queryAmsProperty,
    queryChgHistoryPage,
    queryRecords,
    querySon,
    updateAmsProperty,
    updateData,
  } from '@/api/ams/amsProperty/amsProperty'
  import { ContainerValueType, ExcelType, IRes, Option } from '@jtypes'
  import JPGlobal from '@jutil'
  import { Icon, SearchTitle } from '@jcomponents'
  import FormItemValue from '@/components/common/container/components/formItemValue.vue'
  import PrintModal from '@/views/modules/ams/amsProperty/compoent/PrintModal.vue'
  import amsStock from '@/views/modules/ams/amsStock/index.vue'
  import { property, propertyColumn } from '@/types/modules/ams/amsProperty'
  import { NDescriptions, NDescriptionsItem, NIcon } from 'naive-ui'
  import NumberRange from '@/components/common/numberRange/index.vue'
  import FilterDocModal from './components/FilterDocModal.vue'
  import { useAmsDict } from '@/types/modules/ams/amsClassify'
  import { useSysStore } from '@/store'
  import { useRouter } from 'vue-router'
  import { queryMmisMaterialApply } from '@/api/mmis/matApply/MaterialApplyWeb'
  import TempComponent from '@/components/common/crud/tempComponent.vue'
  // 导入ionicons5图标
  import {
    FilterOutline,
    CheckmarkCircleOutline,
    CloseOutline,
    SearchOutline,
    ListOutline,
    GitNetworkOutline,
    CalendarOutline,
    CalculatorOutline,
    BusinessOutline,
    DocumentTextOutline,
    FilterCircleOutline,
  } from '@vicons/ionicons5'

  export default defineComponent({
    name: '资产详情11',
    methods: { queryMmisMaterialApply },
    components: { TempComponent, amsStock, FormItemValue, PrintModal, NumberRange, CloseOutline, SearchOutline, FilterDocModal, DocumentTextOutline },
    props: {
      // 非固定资产模式
      NotFixedAssetMode: {
        type: Boolean,
        default: false,
      },
    },
    setup(props, ctx) {
      const sysStore = useSysStore()
      const router = useRouter()
      const tableRef = ref()
      const tableName = ref()
      const crud = ref()
      const amsDict = useAmsDict()
      const expandedNames = ref(['details'])
      const amsStockRef = ref()

      // let amsDict.dict = ref<any>([])
      // setPropertyOptions(amsDict.dict)

      let reactivePropertyColumn = computed(() => {
        if (props.NotFixedAssetMode) {
          propertyColumn[0].title = '非固定资产码'
          propertyColumn[0].width = 150
        } else {
          propertyColumn[0].title = '固定资产码'
          propertyColumn[0].width = 110
        }
        return propertyColumn
      })

      onBeforeMount(async () => {
        // getAmsDict().then((res: any) => {
        //   amsDict.dict.value = res
        data.tabs.value = [
          {
            name: '0',
            tab: '未审核资产',
            columns: [
              {
                title: '#',
                key: 'ch1',
                type: ContainerValueType.SELECTION,
                show: false,
                multiple: true,
                // hide: true,
              },
              {
                title: '详情',
                key: 'detail',
                show: false,
                align: 'center',
                width: 100,
                fixed: 'left',
                

                // hide: true,
                render: (rowData: any) =>
                  h(Icon, {
                    name: 'preview',
                    width: 20,
                    height: 20,
                    style: { cursor: 'pointer', marginRight: '10px' },
                    onClick: async () => {
                      methods.showDetail(rowData)
                      debugger
                    },
                  }),
              },
              ...reactivePropertyColumn.value,
            ],
            tabChange: methods.tabChange,
          },
          {
            name: '1',
            tab: '已审核资产',
            columns: [
              {
                title: '#',
                key: 'ch1',
                type: ContainerValueType.SELECTION,
                show: false,
                multiple: true,
                // hide: true,
              },
              {
                title: '详情',
                key: 'detail',
                show: false,
                align: 'center',
                width: 100,
                // hide: true,
                render: (rowData: any) =>
                  h(Icon, {
                    name: 'preview',
                    width: 20,
                    height: 20,
                    style: { cursor: 'pointer', marginRight: '10px' },
                    onClick: () => {
                      methods.showDetail(rowData)
                    },
                  }),
              },
              ...reactivePropertyColumn.value,
            ],
            tabChange: methods.tabChange,
          },
          {
            name: '4',
            tab: '未审核资产子项',
            columns: [
              {
                title: '#',
                key: 'ch1',
                type: ContainerValueType.SELECTION,
                // show: false,
                multiple: true,
                // hide: true,
              },
              {
                title: '详情',
                key: 'detail',
                show: false,
                align: 'center',
                width: 100,
                // hide: true,
                render: (rowData: any) =>
                  h(Icon, {
                    name: 'preview',
                    width: 20,
                    height: 20,
                    style: { cursor: 'pointer', marginRight: '10px' },
                    onClick: () => {
                      methods.showDetail(rowData)
                    },
                  }),
              },
              // 属性列配置，通过...展开操作符插入
              ...reactivePropertyColumn.value,
            ],
            // 标签切换事件处理方法
            tabChange: methods.tabChange,
          },
          {
            name: '5',
            tab: '已审核资产子项',
            columns: [
              {
                title: '#',
                key: 'ch1',
                type: ContainerValueType.SELECTION,
                // show: false,
                multiple: true,
                // hide: true,
              },
              {
                title: '详情',
                key: 'detail',
                show: false,
                align: 'center',
                width: 100,
                // hide: true,
                render: (rowData: any) =>
                  h(Icon, {
                    name: 'preview',
                    width: 20,
                    height: 20,
                    style: { cursor: 'pointer', marginRight: '10px' },
                    onClick: () => {
                      methods.showDetail(rowData)
                    },
                  }),
              },
              // 属性列配置，通过...展开操作符插入
              ...reactivePropertyColumn.value,
            ],
            // 标签切换事件处理方法
            tabChange: methods.tabChange,
          },
          {
            name: '6',
            tab: '已注销资产',
            columns: [
              {
                title: '#',
                key: 'ch1',
                type: ContainerValueType.SELECTION,
                show: false,
                multiple: true,
                // hide: true,
              },
              {
                title: '详情',
                key: 'detail',
                show: false,
                align: 'center',
                width: 100,
                // hide: true,
                render: (rowData: any) =>
                  h(Icon, {
                    name: 'preview',
                    width: 20,
                    height: 20,
                    style: { cursor: 'pointer', marginRight: '10px' },
                    onClick: () => {
                      methods.showDetail(rowData)
                    },
                  }),
              },
              // 属性列配置，通过...展开操作符插入
              ...reactivePropertyColumn.value,
            ],
            // 标签切换事件处理方法
            tabChange: methods.tabChange,
          },
        ]
        data.init.value = true

        // })
      })

      let showScreenForm = ref(true)
      let showFilterPanel = ref(false) // 控制筛选面板显示

      // 数字筛选操作符选项
      const numberOperatorOptions = [
        { label: '等于', value: 'eq' },
        { label: '大于', value: 'gt' },
        { label: '大于等于', value: 'gte' },
        { label: '小于', value: 'lt' },
        { label: '小于等于', value: 'lte' },
        { label: '范围', value: 'between' },
      ]

      // 日期筛选操作符选项
      const dateOperatorOptions = [
        { label: '等于', value: 'eq' },
        { label: '大于', value: 'gt' },
        { label: '大于等于', value: 'gte' },
        { label: '小于', value: 'lt' },
        { label: '小于等于', value: 'lte' },
        { label: '范围', value: 'between' },
      ]

      let methods = {
        // toggleDev() {
        //   this.showFirstDev = !this.showFirstDev; // 切换显示状态
        // },

        calcContentMaxHeight: async () => {
          await nextTick()
          const dom = document.querySelector('.j-container-content-top')
          if (dom) {
            const clientHeight = dom.clientHeight
            let heigth = data.originalContentMaxHeight
            if (!heigth) {
              data.originalContentMaxHeight = crud.value.contentMaxHeight
              heigth = crud.value.contentMaxHeight
            }
            crud.value.contentMaxHeight = heigth.slice(0, heigth.length - 2) - clientHeight + 'px'
          }
        },
        querySummary: async () => {
          if (tableName.value != '1' && tableName.value != '0') {
            return
          }
          if (!data.showSummary.value) {
            return
          }
          const queryForm: any = JPGlobal.deepCopy(data.queryForm.value)
          queryForm.pageSize = 1
          queryForm.pageNum = 1
          //已审核资产汇总
          if (tableName.value == '1') {
            queryForm.isChk = '1'
            queryForm.type = '1'
            // 非固定资产模式
            if (props.NotFixedAssetMode) {
              queryForm.type = '2'
            }

            queryForm.summary = '1'
          }
          //未审核资产汇总
          if (tableName.value == '0') {
            queryForm.isChk = '0'
            queryForm.type = '1'
            // 非固定资产模式
            if (props.NotFixedAssetMode) {
              queryForm.type = '2'
            }
            queryForm.summary = '1'
          }
          if (data.showSummary.value) {
            data.showSummaryLoding.value = true
          }
          queryAmsProperty(queryForm).then(res => {
            if (res.data.total == 0) {
              if (data.showSummary.value) {
                data.showSummaryLoding.value = false
              }
              data.queryResult.value = JPGlobal.clearObject(data.queryResult.value)
              return
            }
            data.queryResult.value = Object.assign(crud.value.originData)
            const firstItem = res.data.records[0]
            const keys = Object.keys(firstItem)
            keys.forEach(key => {
              data.queryResult.value[key] = firstItem[key]
            })
            if (data.showSummary.value) {
              data.showSummaryLoding.value = false
            }
          })
        },
        switchSummary: async () => {
          // 关闭筛选面板
          // showFilterPanel.value = false
          data.showSummary.value = !data.showSummary.value
          await methods.calcContentMaxHeight()
          if (data.showSummary.value) {
            await methods.querySummary()
          }
        },

        // 新增方法：切换筛选面板
        switchFilter: async () => {
          // 关闭合计面板
          // data.showSummary.value = false
          showFilterPanel.value = !showFilterPanel.value
          await methods.calcContentMaxHeight()
        },

        // 新增方法：关闭筛选面板
        closeFilterPanel: async () => {
          showFilterPanel.value = false
          await methods.calcContentMaxHeight()
        },
        queryAmsProperty,
        deleteAmsProperty,
        scroll: (index: number) => {
          tableRef.value.scrollTo({ top: 45 * index })
        },
        showDetail: async (rowData: any) => {
          queryRecords({ faCode: rowData.faCode }).then((res: IRes) => {
            data.imgData.value = res.data
            // debugger
            data.modalData.value = JPGlobal.deepCopy(toRaw(rowData))
            data.showModal.value = true
          })

          queryChgHistoryPage({
            faCode: rowData.faCode,
            pageNum: 1,
            pageSize: 10000,
          }).then((res: IRes) => {
            data.changeData.value = res.data.records
          })

          querySon({
            faCode: rowData.faCode,
            pageNum: 1,
            pageSize: 10000,
          }).then((res: IRes) => {
            data.sonData.value = res.data.records
          })
          // queryChgHistory({faCode: rowData.faCode}).then((res: IRes) => {
          //   data.chgData.value = res.data
          // })
          //把查询条件给tabs
          data.detailsForm.value.faCode = rowData.faCode
          //初始化查询
          // data.detailsTabsCrudRef.value
        },
        saveData: (row: any) => {
          let params = JPGlobal.deepCopy(row.data)
          const form = new FormData()
          Object.keys(params).forEach(key => {
            form.append(key, params[key])
          })
          let list = toRaw(row.img)
          list!.forEach((item: any, index: any) => {
            Object.keys(item).forEach(key => {
              if (item[key]) form.append('records[' + index + '].' + key, item[key])
            })
          })
          updateData(form).then((res: IRes) => {
            if (res.code == 200) {
              window.$message.success('修改成功')
              data.showModal.value = false
              data.modalTableData.value = []
              data.modalData.value = {}
              crud.value.queryData()
            }
          })
        },
        fnToChk: () => {
          if (data.checkedKeys.value.length < 1) {
            window.$message.error('请选择要审核的资产')
          } else {
            updateAmsProperty({ type: '1', ids: data.checkedKeys.value, isChk: '1' }).then((res: IRes) => {
              if (res.code == 200) {
                window.$message.success('审核成功')
                crud.value.queryData()
              }
            })
          }
        },
        fnUnToChk: () => {
          if (data.checkedKeys.value.length < 1) {
            window.$message.error('请选择要销审的资产')
          } else {
            updateAmsProperty({ type: '1', ids: data.checkedKeys.value, isChk: '0' }).then((res: IRes) => {
              if (res.code == 200) {
                window.$message.success('销审成功')
                crud.value.queryData()
              }
            })
          }
        },
        printLabel: () => {
          if (data.checkedKeys.value.length < 1) {
            window.$message.error('请选择要打印的资产')
          } else {
            // 如果不分页，设置打印为纵向，缩放比例为60%
            data.showPrint.value = true
          }
        },
        tabChange(tab: JTab) {
          methods.calcContentMaxHeight()
          //其他页面设置分页
          //未审核资产
          data.showUnToChk.value = false
          data.showChk.value = false
          data.showOperation.value = false
          data.queryForm.value.isCanc = '0'
          if (tab.name == '0') {
            data.queryForm.value.isChk = '0'
            data.queryForm.value.type = '1'
            // 非固定资产模式
            if (props.NotFixedAssetMode) {
              data.queryForm.value.type = '2'
            }
            data.showChk.value = true
            data.showOperation.value = true
          }
          //已审核资产
          if (tab.name == '1') {
            data.showPrintBtn.value = true
            data.queryForm.value.isChk = '1'
            data.queryForm.value.type = '1'
            // 非固定资产模式
            if (props.NotFixedAssetMode) {
              data.queryForm.value.type = '2'
            }
            data.showUnToChk.value = true
          } else {
            data.showPrintBtn.value = false
          }

          //未审核资产子项
          if (tab.name == '4') {
            data.showChk.value = true
            data.showSummary.value = false
            data.queryForm.value.isChk = '0'
            data.queryForm.value.type = '3'
            data.showOperation.value = true
          }

          //已审核资产子项
          if (tab.name == '5') {
            data.showPrintBtn.value = true

            data.showSummary.value = false
            data.showUnToChk.value = true
            data.queryForm.value.isChk = '1'
            data.queryForm.value.type = '3'
          } else if (tab.name != '1') {
          }

          //已审核资产子项
          if (tab.name == '6') {
            data.showSummary.value = false
            data.showChk.value = false
            data.showUnToChk.value = false
            data.queryForm.value.isChk = ''
            data.queryForm.value.type = '1'
            data.queryForm.value.isCanc = '1'
          }
          data.showEdit.value = ['1', '3', '0', '6'].includes(tab.name)
          data.checkedKeys.value = []
          tableName.value = tab.name
        },
        tableFilter: (val: any, filed: string) => {
          if (val) {
            data.filterObj[filed] = val
          } else {
            data.filterObj[filed] = null
          }
          tableRef.value.filter(data.filterObj)
        },

        // 新增方法：按数据类型筛选
        typeFilter: (val: any) => {
          if (val) {
            data.filterObj['type'] = val
          } else {
            data.filterObj['type'] = null
          }
          tableRef.value.filter(data.filterObj)
        },
        advancedFilteringApplyAndQuery: (initiateQuery=true) => {


          // 构建扩展筛选条件
          let extendForm: any = []
          data.propertyData.value.forEach((item: any) => {
            if (item.searchType && item.searchData) {
              // 构建符合后端要求的筛选条件格式
              const formItem: any = {
                title: item.title,
                key: item.key,
                realKey: item.realKey || item.key,
                type: item.type,
                disabled: item.disabled || false,
                searchType: item.searchType,
                searchData: item.searchData,
              }

              // 处理不同数据类型的searchData格式
              if (item.type === 'date') {
                // 日期筛选处理 - 使用统一的处理方法
                if (item.dateOperator) {
                  formItem.searchData = methods.processDateFilter(item)
                  formItem.dateOperator = item.dateOperator
                } else if (Array.isArray(item.searchData)) {
                  // 兼容旧的日期范围处理
                  formItem.searchData = item.searchData.join(',')
                }
              } else if (item.type === 'number') {
                // 数字筛选处理 - 使用统一的处理方法
                formItem.searchData = methods.processNumberFilter(item)
                formItem.numberOperator = item.numberOperator
              }

              extendForm.push(formItem)
            }
          })

          // 更新查询表单
          data.queryForm.value.extendForm = extendForm

          if (initiateQuery) {
            // 执行查询
            crud.value.queryData()
          }
        },
        fnClear: (val: any) => {
          val.searchData = null
          val.searchType = null

          // 如果是数字类型，清除数字筛选相关属性
          if (val.type === 'number') {
            val.numberOperator = 'eq'
            val.numberValue = null
            val.numberMin = null
            val.numberMax = null
          }

          // 如果是日期类型，清除日期筛选相关属性
          if (val.type === 'date') {
            val.dateOperator = 'eq'
            val.dateValue = null
            val.dateMin = null
            val.dateMax = null
          }

          // 清除条件后立即更新extendForm
          methods.updateExtendForm()
        },

        // 新增方法：更新extendForm
        updateExtendForm: () => {
          let extendForm: any = []
          data.propertyData.value.forEach((item: any) => {
            if (item.searchType && item.searchData) {
              const formItem: any = {
                title: item.title,
                key: item.key,
                realKey: item.realKey || item.key,
                type: item.type,
                disabled: item.disabled || false,
                searchType: item.searchType,
                searchData: item.searchData,
              }

              // 处理不同数据类型的searchData格式
              if (item.type === 'date') {
                // 日期筛选处理 - 使用统一的处理方法
                if (item.dateOperator) {
                  formItem.searchData = methods.processDateFilter(item)
                  formItem.dateOperator = item.dateOperator
                } else if (Array.isArray(item.searchData)) {
                  // 兼容旧的日期范围处理
                  formItem.searchData = item.searchData.join(',')
                }
              } else if (item.type === 'number') {
                // 数字筛选处理 - 使用统一的处理方法
                formItem.searchData = methods.processNumberFilter(item)
                formItem.numberOperator = item.numberOperator
              }

              extendForm.push(formItem)
            }
          })

          data.queryForm.value.extendForm = extendForm
        },

        // 新增方法：获取当前激活的筛选条件数量
        getActiveFiltersCount: () => {
          return data.propertyData.value.filter((item: any) => item.searchType).length
        },

        // 新增方法：获取当前激活的筛选条件
        getActiveFilters: () => {
          return data.propertyData.value.filter((item: any) => item.searchType)
        },

        // 新增方法：清空所有筛选条件
        clearAllFilters: () => {
          data.propertyData.value.forEach((item: any) => {
            if (item.searchType) {
              item.searchData = null
              item.searchType = null

              // 如果是数字类型，清除数字筛选相关属性
              if (item.type === 'number') {
                item.numberOperator = 'eq'
                item.numberValue = null
                item.numberMin = null
                item.numberMax = null
              }

              // 如果是日期类型，清除日期筛选相关属性
              if (item.type === 'date') {
                item.dateOperator = 'eq'
                item.dateValue = null
                item.dateMin = null
                item.dateMax = null
              }
            }
          })

          // 清空后更新extendForm
          methods.updateExtendForm()
        },

        // 新增方法：根据字段类型获取图标组件
        getFieldIcon: (type: string) => {
          const iconMap: Record<string, any> = {
            select: ListOutline,
            tree_select: GitNetworkOutline,
            date: CalendarOutline,
            number: CalculatorOutline,
            org: BusinessOutline,
            input: DocumentTextOutline,
          }
          return iconMap[type] || DocumentTextOutline
        },

        // 新增方法：根据字段类型获取颜色
        getFieldColor: (type: string) => {
          const colorMap: Record<string, string> = {
            select: '#2080f0',
            tree_select: '#18a058',
            date: '#f0a020',
            number: '#d03050',
            org: '#722ed1',
            input: '#666666',
          }
          return colorMap[type] || '#666666'
        },

        // 新增方法：获取原始索引（用于定位）
        getOriginalIndex: (item: any) => {
          return data.propertyData.value.findIndex((prop: any) => prop.key === item.key)
        },

        // 数字筛选处理 - 统一为数组格式的通用方法
        processNumberFilter: (item: any) => {
          if (!item.numberOperator) {
            return [null, null]
          }

          switch (item.numberOperator) {
            case 'eq':
              return [item.numberValue, item.numberValue]
            case 'gt':
            case 'gte':
              // 大于、大于等于：[value, null]
              return [item.numberValue, null]
            case 'lt':
            case 'lte':
              // 小于、小于等于：[null, value]
              return [null, item.numberValue]
            case 'between':
              // 范围：[min, max]
              return [item.numberMin, item.numberMax]
            default:
              return [null, null]
          }
        },

        // 日期筛选处理 - 统一为数组格式的通用方法
        processDateFilter: (item: any) => {
          if (!item.dateOperator) {
            return [null, null]
          }

          switch (item.dateOperator) {
            case 'eq':
              return [item.dateValue, item.dateValue]
            case 'gt':
            case 'gte':
              // 大于、大于等于：[value, null]
              return [item.dateValue, null]
            case 'lt':
            case 'lte':
              // 小于、小于等于：[null, value]
              return [null, item.dateValue]
            case 'between':
              // 范围：[min, max]
              return [item.dateMin, item.dateMax]
            default:
              return [null, null]
          }
        },

        // 新增方法：更新数字筛选条件
        updateNumberFilter: (item: any) => {
          if (item.searchType === '3') {
            item.searchData = null
            return
          }

          if (!item.numberOperator) {
            item.searchData = null
            return
          }

          // 使用统一的数字筛选处理方法
          const result = methods.processNumberFilter(item)

          // 验证数据有效性
          if (item.numberOperator === 'between') {
            // 范围操作符需要检查至少有一个值
            if (
              (item.numberMin !== null && item.numberMin !== undefined) ||
              (item.numberMax !== null && item.numberMax !== undefined)
            ) {
              item.searchData = result
            } else {
              item.searchData = null
            }
          } else {
            // 其他操作符需要检查numberValue
            if (item.numberValue !== null && item.numberValue !== undefined) {
              item.searchData = result
            } else {
              item.searchData = null
            }
          }

          // 更新extendForm
          methods.updateExtendForm()
        },

        // 新增方法：更新日期筛选条件
        updateDateFilter: (item: any) => {
          if (item.searchType === '3') {
            item.searchData = null
            return
          }

          if (!item.dateOperator) {
            item.searchData = null
            return
          }

          // 使用统一的日期筛选处理方法
          const result = methods.processDateFilter(item)

          // 验证数据有效性
          if (item.dateOperator === 'between') {
            // 范围操作符需要检查至少有一个值
            if (
              (item.dateMin !== null && item.dateMin !== undefined) ||
              (item.dateMax !== null && item.dateMax !== undefined)
            ) {
              item.searchData = result
            } else {
              item.searchData = null
            }
          } else {
            // 其他操作符需要检查dateValue
            if (item.dateValue !== null && item.dateValue !== undefined) {
              item.searchData = result
            } else {
              item.searchData = null
            }
          }

          // 更新extendForm
          methods.updateExtendForm()
        },

        // 新增方法：获取筛选条件预览文本
        getFilterPreview: (item: any) => {
          if (!item.searchData || item.searchType === '3') {
            return item.searchType === '3' ? '未录入' : ''
          }

          const typeText = item.searchType === '1' ? '精确' : '模糊'

          // 处理选择类型 - 显示label
          if (item.type === 'select' && item.searchData) {
            const options = item.options || amsDict.dict[item.optionKey] || []
            const selectedOption = options.find(
              (opt: any) => opt.value === item.searchData || opt.code === item.searchData
            )
            const displayText = selectedOption
              ? selectedOption.label || selectedOption.name || selectedOption.text
              : item.searchData
            return `${typeText}：${displayText}`
          }

          // 处理树选择类型 - 显示label
          if (item.type === 'tree_select' && item.searchData) {
            const options = item.options || amsDict.dict[item.optionKey] || []
            const keyField = item.treeProp ? item.treeProp.key : 'key'
            const labelField = item.treeProp ? item.treeProp.label : 'label'

            // 递归查找树形结构中的选项
            const findTreeOption = (nodes: any[], value: any): any => {
              for (const node of nodes) {
                if (node[keyField] === value) {
                  return node
                }
                if (node.children && node.children.length > 0) {
                  const found = findTreeOption(node.children, value)
                  if (found) return found
                }
              }
              return null
            }

            const selectedOption = findTreeOption(options, item.searchData)
            const displayText = selectedOption ? selectedOption[labelField] : item.searchData
            return `${typeText}：${displayText}`
          }

          // 处理日期筛选
          if (item.type === 'date') {
            if (item.dateOperator) {
              const operatorMap: Record<string, string> = {
                eq: '等于',
                gt: '大于',
                gte: '大于等于',
                lt: '小于',
                lte: '小于等于',
                between: '范围',
              }

              const operatorText = operatorMap[item.dateOperator] || '未知'

              if (item.dateOperator === 'between') {
                if (item.dateMin !== null && item.dateMax !== null) {
                  return `${typeText}：${item.dateMin} ~ ${item.dateMax}`
                } else if (item.dateMin !== null) {
                  return `${typeText}：≥ ${item.dateMin}`
                } else if (item.dateMax !== null) {
                  return `${typeText}：≤ ${item.dateMax}`
                }
              } else if (item.dateValue !== null && item.dateValue !== undefined) {
                return `${typeText}：${operatorText} ${item.dateValue}`
              }
            } else if (Array.isArray(item.searchData)) {
              // 兼容旧的日期范围格式
              return `${typeText}：${item.searchData[0]} ~ ${item.searchData[1]}`
            }
          }

          // 处理数字筛选
          else if (item.type === 'number' && item.numberOperator) {
            const operatorMap: Record<string, string> = {
              eq: '等于',
              // gt: '大于',
              gte: '大于等于',
              // lt: '小于',
              lte: '小于等于',
              between: '范围',
            }

            const operatorText = operatorMap[item.numberOperator] || '未知'

            if (item.numberOperator === 'between') {
              if (item.numberMin !== null && item.numberMax !== null) {
                return `${typeText}：${item.numberMin} ~ ${item.numberMax}`
              } else if (item.numberMin !== null) {
                return `${typeText}：≥ ${item.numberMin}`
              } else if (item.numberMax !== null) {
                return `${typeText}：≤ ${item.numberMax}`
              }
            } else if (item.numberValue !== null && item.numberValue !== undefined) {
              return `${typeText}：${operatorText} ${item.numberValue}`
            }
          }

          // 处理组织机构类型
          else if (item.type === 'org' && item.searchData) {
            // 如果是数组，显示选中数量
            if (Array.isArray(item.searchData)) {
              return `${typeText}：已选择 ${item.searchData.length} 个组织`
            }
            return `${typeText}：${item.searchData}`
          }

          // 处理普通文本
          else if (typeof item.searchData === 'string' && item.searchData.trim()) {
            return `${typeText}：${item.searchData}`
          }

          return ''
        },

        //   详情页detailsTabs切换
        detailsTabChange: (tab: JTab) => {
          data.tabStatus.value = tab.name
        },
        queryComplete: () => {
          methods.querySummary()
        },

        // 新增方法用于处理历史数据查询弹窗
        queryHistoryData: () => {
          data.selectedHistoryMonth.value = data.queryForm.value.queryHistoryDataMonth
          data.showHistoryModal.value = true
        },
        confirmHistorySelection: () => {
          data.queryForm.value.queryHistoryDataMonth = data.selectedHistoryMonth.value
          data.showHistoryModal.value = false
          crud.value.queryData() // 触发数据刷新
        },
        cancelHistorySelection: () => {
          data.showHistoryModal.value = false
        },
      }

      const property0 = property.map(item => {
        return {
          ...item,
          disabled: false,
        }
      })
      let data: any = {
        changeData: ref([]),
        sonData: ref([]),
        recordHeight: ref(300),
        showPrintBtn: ref(false),
        showSummaryLoding: ref(false),
        originalContentMaxHeight: '',
        showSummary: ref(false),
        tabContentRender: ref(() => {
          return h(
            NDescriptions,
            { colum: '3', bordered: true, title: '', labelPlacement: 'top' },
            {
              default: () => [
                h(
                  NDescriptionsItem,
                  { label: '未注销' },
                  {
                    default: () => [
                      h('div', null, {
                        default: () => '原值: ' + (data.queryResult.value.totalNRassetNav || 0),
                      }),
                      h('div', null, {
                        default: () => '净值: ' + (data.queryResult.value.totalNRnbv || 0),
                      }),
                      h('div', null, {
                        default: () => '累计折旧: ' + (data.queryResult.value.totalNRdep || 0),
                      }),
                      h('div', null, {
                        default: () => '数量: ' + (data.queryResult.value.totalNRnum || 0),
                      }),
                    ],
                  }
                ),
                h(
                  NDescriptionsItem,
                  { label: '已注销' },
                  {
                    default: () => [
                      h('div', null, {
                        default: () => [
                          h('div', null, {
                            default: () => '原值: ' + (data.queryResult.value.totalHRassetNav || 0),
                          }),
                          h('div', null, {
                            default: () => '净值: ' + (data.queryResult.value.totalHRnbv || 0),
                          }),
                          h('div', null, {
                            default: () => '累计折旧: ' + (data.queryResult.value.totalHRdep || 0),
                          }),
                          h('div', null, {
                            default: () => '数量: ' + (data.queryResult.value.totalHRnum || 0),
                          }),
                        ],
                      }),
                    ],
                  }
                ),
                h(
                  NDescriptionsItem,
                  { label: '总计' },
                  {
                    default: () => [
                      h('div', null, {
                        default: () => [
                          h('div', null, {
                            default: () => '原值: ' + (data.queryResult.value.totalALassetNav || 0),
                          }),
                          h('div', null, { default: () => '净值: ' + (data.queryResult.value.totalALnbv || 0) }),
                          h('div', null, { default: () => '累计折旧: ' + (data.queryResult.value.totalALdep || 0) }),
                          h('div', null, { default: () => '数量: ' + (data.queryResult.value.totalALnum || 0) }),
                        ],
                      }),
                    ],
                  }
                ),
              ],
            }
          )
        }),
        init: ref(false),
        showMenu: ref(false),
        showChk: ref(false),
        chgData: ref(),
        showOperation: ref(),
        // sonData: ref(),
        propertyData: ref<Array<any>>([]),
        showEdit: ref(true),
        showUnToChk: ref(true),
        showScreen: ref(false),
        filterObj: reactive<any>({ title: [], type: null }),
        screenColumn: reactive<Array<CRUDColumnInterface>>([
          {
            title: () =>
              h(
                SearchTitle,
                {
                  onSearch: (val: any) => {
                    methods.tableFilter(val, 'title')
                  },
                  searchType: 'tree',
                  treeNode: { label: 'title', value: 'key' },
                  // options: property0,
                  options: data.propertyData.value,
                },
                () => '字段名称'
              ),
            key: 'title',
            filter(value: any, row: any) {
              if (row.key) {
                return row.key == value
              }
            },
            width: 120,
          },
          {
            title: () =>
              h(
                SearchTitle,
                {
                  onSearch: (val: any) => {
                    methods.typeFilter(val)
                  },
                  searchType: 'select',
                  options: [
                    { label: '文本', value: 'input' },
                    { label: '选择', value: 'select' },
                    { label: '树选择', value: 'tree_select' },
                    { label: '日期', value: 'date' },
                    { label: '数字', value: 'number' },
                    { label: '组织', value: 'org' },
                  ],
                },
                () => '数据类型'
              ),
            key: 'type',
            width: 90,
            filter(value: any, row: any) {
              return row.type === value
            },
            render: (row: any) => {
              const typeMap: Record<string, { label: string; color: string; icon: any }> = {
                select: { label: '选择', color: '#2080f0', icon: ListOutline },
                tree_select: { label: '树选择', color: '#18a058', icon: GitNetworkOutline },
                date: { label: '日期', color: '#f0a020', icon: CalendarOutline },
                number: { label: '数字', color: '#d03050', icon: CalculatorOutline },
                org: { label: '组织', color: '#722ed1', icon: BusinessOutline },
                input: { label: '文本', color: '#666666', icon: DocumentTextOutline },
              }

              const typeInfo = typeMap[row.type] || { label: '文本', color: '#666666', icon: DocumentTextOutline }

              return h(
                'div',
                {
                  style: {
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px',
                    padding: '2px 6px',
                    borderRadius: '4px',
                    backgroundColor: typeInfo.color + '15',
                    border: `1px solid ${typeInfo.color}30`,
                    fontSize: '11px',
                    fontWeight: '500',
                  },
                },
                [
                  h(
                    NIcon,
                    {
                      size: 12,
                      style: { color: typeInfo.color },
                    },
                    {
                      default: () => h(typeInfo.icon),
                    }
                  ),
                  h('span', { style: { color: typeInfo.color } }, typeInfo.label),
                ]
              )
            },
          },
          {
            title: '筛选类型',
            key: 'searchType',
            width: 120,
            render: (row: any) => {
              let options = []
              if (row.type == ContainerValueType.SELECT || row.type == ContainerValueType.TREE_SELECT) {
                options = [
                  { label: '精确', value: '1', type: 'primary' },
                  { label: '未录入', value: '3', type: 'warning' },
                ]
              } else if (row.type == ContainerValueType.DATE) {
                options = [
                  { label: '精确', value: '1', type: 'primary' },
                  { label: '未录入', value: '3', type: 'warning' },
                ]
              } else if (row.type == ContainerValueType.NUMBER) {
                options = [
                  { label: '精确', value: '1', type: 'primary' },
                  { label: '未录入', value: '3', type: 'warning' },
                ]
              } else {
                options = [
                  { label: '精确', value: '1', type: 'primary' },
                  { label: '模糊', value: '2', type: 'info' },
                  { label: '未录入', value: '3', type: 'warning' },
                ]
              }

              // 创建按钮组渲染函数
              const getNodeFn = (width: number, repeatSelectionCancel=false) =>
                h(
                  'div',
                  {
                    style: {
                      width: width + '%',
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '4px',
                    },
                  },
                  [
                    h(
                      'div',
                      {
                        style: {
                          display: 'flex',
                          gap: '4px',
                          flexWrap: 'wrap',
                        },
                      },
                      options.map(option =>
                        h(
                          'button',
                          {
                            class: [
                              'filter-type-btn',
                              row.searchType === option.value
                                ? `filter-type-btn--${option.type}`
                                : 'filter-type-btn--default',
                            ],
                            style: {
                              padding: '2px 8px',
                              fontSize: '11px',
                              borderRadius: '4px',
                              cursor: 'pointer',
                              transition: 'all 0.2s ease',
                              backgroundColor:
                                row.searchType === option.value
                                  ? option.type === 'primary'
                                    ? '#2080f0'
                                    : option.type === 'info'
                                    ? '#17a2b8'
                                    : option.type === 'warning'
                                    ? '#f0a020'
                                    : '#6c757d'
                                  : '#f8f9fa',
                              color: row.searchType === option.value ? '#ffffff' : '#495057',
                              border: row.searchType === option.value ? 'none' : '1px solid #dee2e6',
                            },
                            onClick: () => {
                              // 如果点击的筛选类型和当前类型一样，则取消筛选
                              if (repeatSelectionCancel&&row.searchType === option.value) {
                                row.searchType = null
                                row.searchData = null

                                // 如果是数字类型，清除数字筛选相关属性
                                if (row.type === 'number') {
                                  row.numberOperator = 'eq'
                                  row.numberValue = null
                                  row.numberMin = null
                                  row.numberMax = null
                                }

                                // 如果是日期类型，清除日期筛选相关属性
                                if (row.type === 'date') {
                                  row.dateOperator = 'eq'
                                  row.dateValue = null
                                  row.dateMin = null
                                  row.dateMax = null
                                }
                              } else {
                                // 设置新的筛选类型
                                row.searchType = option.value
                              }
                            },
                          },
                          option.label
                        )
                      )
                    ),
                  ]
                )

              row.getNodeFnselectType = getNodeFn
              return getNodeFn(100,true)
            },
          },
          {
            title: '操作',
            key: 'action',
            width: 60,
            align: 'center',
            render: (row: any) => {
              // 如果已经添加了筛选条件，显示移除按钮
              if (row.searchType) {
                return h(
                  'span',
                  {
                    style: {
                      color: '#d03050',
                      cursor: 'pointer',
                      fontSize: '12px',
                      fontWeight: '500',
                      padding: '2px 6px',
                      borderRadius: '4px',
                      border: '1px solid #d03050',
                      backgroundColor: '#d0305015',
                      transition: 'all 0.2s ease',
                    },
                    onClick: () => {
                      methods.fnClear(row)
                    },
                    onMouseenter: (e: any) => {
                      e.target.style.backgroundColor = '#d03050'
                      e.target.style.color = '#ffffff'
                    },
                    onMouseleave: (e: any) => {
                      e.target.style.backgroundColor = '#d0305015'
                      e.target.style.color = '#d03050'
                    },
                  },
                  '移除'
                )
              } else {
                // 未添加筛选条件时显示添加提示
                return h(
                  'span',
                  {
                    style: {
                      color: '#999',
                      fontSize: '12px',
                      cursor: 'default',
                    },
                  },
                  '-'
                )
              }
            },
          },
          // {
          //   title: '筛选值',
          //   key: 'searchData',
          //   width: 100,
          //   tableColumnShow:true,
          //   show:false,
          //   render: (row: any) => {
          //     if (row.type == ContainerValueType.SELECT) {
          //       return h(NSelect, {
          //         style: {width: '80%'},
          //         filterable: true,
          //         disabled: row.searchType == '3',
          //         multiple: true,
          //         value: row.searchData,
          //         clearable: true,
          //         maxTagCount: 'responsive',
          //         options: amsDict.dict[row.optionKey] as any,
          //       })
          //     } else if (row.type == ContainerValueType.TREE_SELECT) {
          //       return h(NTreeSelect, {
          //         style: {width: '80%'},
          //         filterable: true,
          //         disabled: row.searchType == '3',
          //         multiple: true,
          //         maxTagCount: 'responsive',
          //         clearable: true,
          //         options: amsDict.dict[row.optionKey] as any,
          //         keyField: row.treeProp ? row.treeProp.key : 'key',
          //         labelField: row.treeProp ? (row.treeProp as any).label : 'label',
          //       })
          //     } else if (row.type == ContainerValueType.DATE) {
          //       // if (!row.searchData){
          //       //   row.searchData=[]
          //       // }
          //
          //       return h(NDatePicker, {
          //         style: {
          //           width: '80%',
          //         },
          //         disabled: row.searchType == '3',
          //         type: 'daterange',
          //         formattedValue: row.searchData,
          //         valueFormat: 'yyyyMMdd',
          //         onUpdateFormattedValue: (val: any) => {
          //           row.searchData = val
          //         },
          //       })
          //     } else if (row.type == ContainerValueType.NUMBER) {
          //       if (!row.searchData){
          //         row.searchData=[null,null]
          //       }
          //
          //       return h(NumberRange, {
          //         value: row.searchData,
          //         onUpdateValue: (val: any) => {
          //           row.searchData = val
          //         },
          //         disabled: row.searchType == '3',
          //         style: {width: '80%'},
          //       })
          //     } else {
          //       return h(NInput, {
          //         value: row.searchData,
          //         onUpdateValue: (val: any) => {
          //           row.searchData = val
          //         },
          //         disabled: row.searchType == '3',
          //         style: {width: '80%'},
          //       })
          //     }
          //   },
          // },
        ]),
        detailsTabsCrudRef: ref(),
        //tabs标识，切换tabs的时候会把名字赋进来，根据tabStatus来计算判断，查询方法使用哪个方法
        tabStatus: ref('0'),

        queryConditionCpd: computed(() => (type: string[]) => {
          return type.includes(data.tabStatus.value)
        }),
        //计算属性：根据tabStatus计算使用查询哪一个tabs的方法,在根据actionMethod1()的传的参数决定使用哪个方法
        actionMethod1: computed(() => {
          switch (data.tabStatus.value) {
            case '0':
              // 查询 变动记录的方法
              // return ()=>{}
              return queryChgHistoryPage

            case '1':
              return querySon
          }
        }),
        detailsForm: ref({
          //变更条件
          //展示类型
          asset: '',
          faCode: '',
        }),
        detailsTabs: ref<JTab[]>([
          {
            name: '0',
            tab: '变动记录',
            columns: [
              { title: '固定资产码', key: 'faCode', width: 120 },
              { title: '资产名称', key: 'assetName', width: 100 },
              { title: '变动类型', key: 'chgType', width: 100 },
              {
                title: '变动前值',
                key: 'chgBefore',
                width: 100,
                render: (row: any) => {
                  return h('span', row.chgBefore || row.chgBeforeName)
                },
              },
              {
                title: '变动后值',
                key: 'chgAfter',
                width: 100,
                render: (row: any) => {
                  return h('span', row.chgAfter || row.chgAfterName)
                },
              },
              { title: '变更时间', key: 'chgDate', width: 100 },
              {
                title: '变更人',
                key: 'chger',
                width: 100,
                render: (row: any) => {
                  return row.chgerName ? `${row.chger} ${row.chgerName}` : row.chger
                },
              },
              {
                title: '审核人',
                key: 'chker',
                width: 100,
                render: (row: any) => {
                  return row.chkerName ? `${row.chker} ${row.chkerName}` : row.chker
                },
              },
              { title: '变动原因', key: 'chgRea', width: 100 },
              { title: '备注', key: 'memo', width: 200 },
              { title: '创建时间', key: 'createTime', width: 100 },
              { title: '变动编号', key: 'chgNo', width: 200 },
            ],
            tabChange: methods.detailsTabChange,
          },
          {
            name: '1',
            tab: '资产子项',
            columns: [
              { title: '固定资产码', key: 'faCode', width: 100 },
              { title: '资产名称', key: 'assetName', width: 100 },
              { title: '资产型号', key: 'assetMol', width: 100 },
              { title: '资产编码', key: 'assetCode', width: 100 },
              { title: '资产二维码', key: 'uid', width: 100 },
              { title: '资产分类', key: 'type', width: 100 },
            ],
            tabChange: methods.detailsTabChange,
          },
        ]),

        chgColumn: reactive<Array<CRUDColumnInterface>>([
          { title: '固定资产码', key: 'faCode', width: 120 },
          { title: '资产名称', key: 'assetName', width: 100 },
          { title: '变动类型', key: 'chgType', width: 100 },
          { title: '变动前值', key: 'chgBefore', width: 100 },
          { title: '变动后值', key: 'chgAfter', width: 100 },
          { title: '变更时间', key: 'chgDate', width: 100 },
          { title: '变更人', key: 'chger', width: 100 },
          { title: '审核人', key: 'chker', width: 100 },
          { title: '变动原因', key: 'chgRea', width: 100 },
          { title: '备注', key: 'memo', width: 100 },
          { title: '创建时间', key: 'createTime', width: 100 },
          { title: '变动编号', key: 'chgNo', width: 100 },
        ]),
        sonColumn: reactive<Array<CRUDColumnInterface>>([
          { title: '固定资产码', key: 'faCode', width: 100 },
          { title: '资产名称', key: 'assetName', width: 100 },
          { title: '资产型号', key: 'assetMol', width: 100 },
          { title: '资产编码', key: 'assetCode', width: 100 },
          { title: '资产二维码', key: 'uid', width: 100 },
          { title: '资产分类', key: 'type', width: 100 },
        ]),
        tabs: ref<JTab[]>([]),
        //分页标识，查询下面的结果，因为是放在Descriptions组件里面的，所以不用分页
        pagingFlag: ref(true),
        queryResult: ref({
          // 未注销的
          totalNRassetNav: 0, // 原值合计
          totalNRdep: 0, // 累计折旧
          totalNRnbv: 0, // 净值合计
          totalNRnum: 0, // 数量合计
          // 已注销的
          totalHRassetNav: 0,
          totalHRdep: 0,
          totalHRnbv: 0,
          totalHRnum: 0,
          // 全部的
          totalALassetNav: 0,
          totalALdep: 0,
          totalALnbv: 0,
          totalALnum: 0,
        }),
        showModal: ref(false),
        checkedKeys: ref([]), // 选中的行
        showPrint: ref(false), // 展示打印页面
        queryForm: ref<any>({
          asset: '',
          type: props.NotFixedAssetMode ? '2' : '1',
          assetStatus: null,
          assetTypeN: '',
          assetType: '',
          deptField: 'dept_use',
          deptCode: '',
          inptDateRange: null,
          extendForm: [],
          isChk: '0',
          queryHistoryDataMonth: null, // 新增历史查询月份字段
        }),
        modalData: ref(),
        imgData: ref(),
        assetTypeOption: ref(),
        modalTableData: ref(),
        clickRow: ref(),
        readonly: ref(false),
        orgFlowStyle: {
          height: '100%',
        },
        deptOptions: <Option[]>[
          { label: '使用科室', value: 'dept_use' },
          { label: '管理科室', value: 'dept' },
          { label: '存放位置', value: 'storage_area' },
        ],
        netWork: ref('1'),
        printOptions: ref(['page']), // 添加打印选项
        showHistoryModal: ref(false), // 新增控制历史月份选择弹窗的变量
        selectedHistoryMonth: ref(null), // 新增存储弹窗中选定月份的变量
        showFilterDoc: ref(false), // 控制筛选文档弹窗显示
      }
      let deptLabel = computed(() => {
        return data.deptOptions.find((option: Option) => option.value === data.queryForm.value.deptField)?.label
      })
      watch(
        () => sysStore.$state.initOnload,
        (newVal: boolean) => {
          if (newVal) {
            data.showMenu.value = JPGlobal.pageButtonAuth('管理员', router.currentRoute.value.path)
          }
        }
      )

      if (sysStore.$state.initOnload) {
        data.showMenu.value = JPGlobal.pageButtonAuth('管理员', router.currentRoute.value.path)
      }

      const exportConfig: ExcelType = {
        borderStyle: true,
        writeFootnoteConfig: {
          enable: true,
          footnote: '资产信息导出：',
          exportUser: true,
          exportDate: true,
        },
        dontExport: ['detail', 'ch1'],
        enableFreeze: true,
        // extraExportColumns:[
        // { title: '资金来源', key: 'source', realKey: 'source', type: ContainerValueType.SELECT}
        // ]
      }

      // 添加打印前的处理逻辑
      const beforePrint = () => {
        const printContainer = document.querySelector('.n-card-content')
        if (printContainer && data.printOptions && !data.printOptions.value.includes('page')) {
          printContainer.classList.add('print-portrait-scale')
        }
      }

      const afterPrint = () => {
        const printContainer = document.querySelector('.n-card-content')
        if (printContainer) {
          printContainer.classList.remove('print-portrait-scale')
        }
      }

      onMounted(async () => {
        let list = [...property].map(item => {
          return {
            ...item,
            key: item?.realKey || item?.key,
            disabled: false,
          }
        })
        // let list = [...property]
        list.unshift({
          title: '未补录图片',
          key: 'nonePictures',
          realKey: 'none_pictures',
          type: ContainerValueType.INPUT,
          disabled: false,
        })
        list.forEach((item: any) => {
          item.searchData = null
          item.searchType = null

          // 为数字类型字段初始化筛选相关属性
          if (item.type === 'number') {
            item.numberOperator = 'eq' // 默认为等于
            item.numberValue = null
            item.numberMin = null
            item.numberMax = null
          }

          // 为日期类型字段初始化筛选相关属性
          if (item.type === 'date') {
            item.dateOperator = 'eq' // 默认为等于
            item.dateValue = null
            item.dateMin = null
            item.dateMax = null
          }
        })

        data.propertyData.value = list
        // list.filter((item: any) => item.key != 'assetType' && item.key != 'assetTypeN')
        data.orgFlowStyle.height = window.innerHeight + 'px'

        // console.log("amsDict.dict",amsDict.dict)

        // 添加打印事件监听
        window.addEventListener('beforeprint', beforePrint)
        window.addEventListener('afterprint', afterPrint)
      })

      // 新增计算属性，动态计算高度
      const stockDivHeight = computed(() => {
        // 移除副作用逻辑
        return expandedNames.value.includes('details') ? 'calc(100% - 300px)' : 'calc(100% - 50px)'
      })

      // 新增 watch 监听展开/收起状态
      watch(expandedNames, () => {
        nextTick(() => {
          setTimeout(() => {
            amsStockRef.value?.container?.adjustHeight(false)
          }, 500)
        })
      })

      onUnmounted(() => {
        // 移除打印事件监听
        window.removeEventListener('beforeprint', beforePrint)
        window.removeEventListener('afterprint', afterPrint)
      })

      return {
        tableName,
        tableRef,
        deptLabel,
        crud,
        showScreenForm,
        showFilterPanel,
        numberOperatorOptions,
        dateOperatorOptions,
        amsDict,
        rangeShortcuts: JPGlobal.getRangeShortcuts(),
        showFirstDev: true, // 初始显示第一个 dev
        expandedNames,
        stockDivHeight,
        exportConfig,
        amsStockRef,
        ...data,
        ...methods,
      }
    },
  })
</script>

<style scoped>
  /* 使用深度选择器确保 j-crud 中的 data-table 高度填满容器 */
  :deep(.j-crud-content) {
    height: 100%;
  }

  :deep(.j-crud-content .n-data-table) {
    height: 100%;
  }

  /* 特别针对详情页中的 j-crud */
  div[style='height: 300px'] {
    display: flex;
    flex-direction: column;
  }

  div[style='height: 300px'] > .j-crud {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  div[style='height: 300px'] > .j-crud :deep(.n-tabs) {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  div[style='height: 300px'] > .j-crud :deep(.n-tabs .n-tab-pane) {
    flex: 1;
    height: 100%;
  }

  div[style='height: 300px'] > .j-crud :deep(.n-data-table) {
    height: 100%;
  }

  /* 打印样式控制 */
  @media print {
    /* 当不分页时，控制打印为纵向，缩放比例为60% */
    .print-portrait-scale {
      transform: scale(0.6);
      transform-origin: top left;
      width: 167% !important; /* 100/0.6 ≈ 167% */
      height: 167% !important;
    }
  }

  /* 筛选面板样式 */
  .filter-panel-container {
    background: #ffffff;
    border: 1px solid #e0e0e6;
    border-radius: 8px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  .filter-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 10px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #e0e0e6;
    border-radius: 8px 8px 0 0;
  }

  .header-left {
    display: flex;
    align-items: center;
  }

  .panel-title {
    font-weight: 600;
    font-size: 16px;
    color: #333;
    margin-right: 12px;
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .filter-panel-content {
    display: flex;
    gap: 16px;
    padding: 4px;
    max-height: 500px;
  }

  .fields-table-container {
    border: 1px solid #e0e0e6;
    border-radius: 6px;
    overflow: hidden;
  }

  .conditions-container {
    max-height: 400px;
    overflow-y: auto;
  }

  /* 重构后的资产筛选弹窗样式 */
  .filter-modal-container {
    display: flex;
    height: 100%;
    gap: 20px;
  }

  .filter-fields-section {
    flex: 0 0 35%;
    display: flex;
    flex-direction: column;
    border: 1px solid #e0e0e6;
    border-radius: 8px;
    background: #fafafa;
    overflow: hidden;
  }

  .filter-conditions-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    border: 1px solid #e0e0e6;
    border-radius: 8px;
    background: #ffffff;
    overflow: hidden;
  }

  .section-header {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #e0e0e6;
    font-weight: 500;
    font-size: 14px;
    color: #333;
  }

  .section-title {
    margin-right: 12px;
    font-weight: 600;
  }

  .field-count,
  .condition-count {
    font-size: 12px;
    color: #666;
  }

  .empty-conditions {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
  }

  .conditions-grid {
    padding: 16px;
  }

  .filter-cards-container {
    display: grid;
    gap: 16px;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }

  .filter-condition-card {
    background: #ffffff;
    border: 1px solid #e8e8f0;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    position: relative;
  }

  .filter-condition-card:hover {
    border-color: #2080f0;
    box-shadow: 0 4px 16px rgba(32, 128, 240, 0.15);
    transform: translateY(-2px);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding-bottom: 4px;
    border-bottom: 1px solid #f0f0f0;
  }

  .field-info {
    display: flex;
    align-items: center;
    flex: 1;
  }

  .field-name {
    font-weight: 500;
    color: #333;
    cursor: pointer;
    transition: color 0.2s ease;
  }

  .field-name:hover {
    color: #2080f0;
  }

  .filter-type-section,
  .filter-value-section {
    margin-bottom: 12px;
  }

  .filter-type-label,
  .filter-value-label {
    display: inline;
    float: left;
    font-size: 12px;
    color: #666;
    margin-bottom: 6px;
    font-weight: 500;
  }

  .filter-type-select {
    width: 100%;
  }

  .filter-value-input {
    width: 100%;
  }

  .filter-value-input :deep(.n-input),
  .filter-value-input :deep(.n-select),
  .filter-value-input :deep(.n-tree-select),
  .filter-value-input :deep(.n-date-picker) {
    width: 100%;
  }

  /* 数字筛选容器样式 */
  .number-filter-container {
    display: flex;
    align-items: center;
    width: 100%;
    gap: 4px;
  }

  .number-filter-container .n-input-number {
    min-width: 0;
  }

  /* 日期筛选容器样式 */
  .date-filter-container {
    display: flex;
    align-items: center;
    width: 100%;
    gap: 4px;
  }

  .date-filter-container .n-date-picker {
    min-width: 0;
  }

  .filter-preview {
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid #f0f0f0;
  }

  .modal-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0 0 0;
  }

  .footer-info {
    flex: 1;
  }

  .footer-actions {
    display: flex;
    gap: 12px;
  }

  /* 卡片动画效果 */
  .filter-card-enter-active,
  .filter-card-leave-active {
    transition: all 0.3s ease;
  }

  .filter-card-enter-from {
    opacity: 0;
    transform: translateY(20px) scale(0.9);
  }

  .filter-card-leave-to {
    opacity: 0;
    transform: translateY(-20px) scale(0.9);
  }

  .filter-card-move {
    transition: transform 0.3s ease;
  }

  /* 响应式设计 */
  @media (max-width: 1200px) {
    .filter-modal-container {
      flex-direction: column;
      gap: 16px;
    }

    .filter-fields-section {
      flex: 0 0 auto;
      max-height: 300px;
    }

    .filter-cards-container {
      grid-template-columns: 1fr;
    }
  }

  /* 筛选类型按钮组样式 */
  .filter-type-btn {
    outline: none;
    white-space: nowrap;
    min-width: 40px;
    font-weight: 500;
  }

  .filter-type-btn:hover {
    opacity: 0.8;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .filter-type-btn:active {
    transform: translateY(0);
  }

  .filter-type-btn--default:hover {
    background-color: #e9ecef !important;
    border-color: #adb5bd !important;
  }

  /* 深色模式适配 */
  :deep(.n-modal) {
    .filter-modal-container {
      .filter-fields-section {
        background: var(--n-color);
        border-color: var(--n-border-color);
      }

      .filter-conditions-section {
        background: var(--n-color);
        border-color: var(--n-border-color);
      }

      .section-header {
        background: var(--n-color-embedded);
        border-color: var(--n-border-color);
        color: var(--n-text-color);
      }

      .filter-condition-card {
        background: var(--n-color);
        border-color: var(--n-border-color);
      }

      .filter-condition-card:hover {
        border-color: var(--n-color-primary);
      }
    }
  }

  /* 深色模式下的按钮样式 */
  :deep(.n-modal) .filter-type-btn--default {
    background-color: var(--n-color-embedded) !important;
    color: var(--n-text-color) !important;
    border-color: var(--n-border-color) !important;
  }

  :deep(.n-modal) .filter-type-btn--default:hover {
    background-color: var(--n-color-embedded-popover) !important;
    border-color: var(--n-border-color-modal) !important;
  }
</style>
