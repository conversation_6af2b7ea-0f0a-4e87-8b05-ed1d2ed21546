<template>
  <!-- <doc-alert title="审批通过、不通过、驳回" url="https://doc.iocoder.cn/bpm/task-todo-done/" /> -->
  <!-- <doc-alert title="审批加签、减签" url="https://doc.iocoder.cn/bpm/sign/" /> -->
  <!-- <doc-alert title="审批转办、委派、抄送" url="https://doc.iocoder.cn/bpm/task-delegation-and-cc/" /> -->
  <!-- <doc-alert title="审批加签、减签" url="https://doc.iocoder.cn/bpm/sign/" /> -->

  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form ref="queryFormRef" :inline="true" :model="queryParams" class="-mb-15px" label-width="68px">
      <el-form-item label="流程名称" prop="name">
        <el-input
          v-model="queryParams.name"
          class="w-240px"
          clearable
          placeholder="请输入流程名称"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="发起人" prop="empCode">
        <j-bus-emp-search
          @update:option="
            val => {
              queryParams.empCode = val?.empCode
            }
          "
          :clearable="true"
        />
      </el-form-item>

      <el-form-item label="发起人科室" prop="deptCode" label-width="100px">
        <j-bus-hos-org
          @update:option="
            val => {
              queryParams.deptCode = val?.orgId
            }
          "
          :clearable="true"
        />
      </el-form-item>

      <el-form-item label="任务名称" prop="taskName">
        <el-input
          v-model="queryParams.taskName"
          class="w-240px"
          clearable
          placeholder="请输入任务名称"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="流程分类" prop="category" class="w-240px">
        <el-select v-model="queryParams.processCategory" placeholder="请选择流程分类" clearable>
          <el-option
            v-for="category in categoryList"
            :key="category.code"
            :label="category.name"
            :value="category.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="业务标识" prop="name">
        <el-input
          v-model="queryParams.businessKey"
          class="w-240px"
          clearable
          placeholder="请输入业务标识"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="w-240px"
          end-placeholder="结束日期"
          start-placeholder="开始日期"
          type="daterange"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>

      <el-form-item>
        <el-button @click="handleQuery" type="primary">
          <Search style="width: 1em; height: 1em; margin-right: 5px"></Search>
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Refresh style="width: 1em; height: 1em; margin-right: 5px"></Refresh>
          重置
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list">
      <el-table-column align="center" label="流程" prop="processInstance.name" width="180" />
      <el-table-column align="center" label="业务标识" prop="processInstance.businessKey" width="180" />
      <el-table-column align="center" label="发起人" prop="processInstance.startUser.empName" width="80" />
      <el-table-column align="center" label="发起人科室" prop="processInstance.startUser.deptName" width="80" />
      <el-table-column align="center" label="发起原因" prop="createReason" width="200" />
      <el-table-column :formatter="dateFormatter" align="center" label="发起时间" prop="createTime" width="120" />
      <el-table-column align="center" label="当前任务" prop="name" width="180" />
      <el-table-column align="center" label="审批状态" prop="status" width="120">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.BPM_TASK_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column :formatter="dateFormatter" align="center" label="任务开始时间" prop="createTime" width="180" />
      <el-table-column :formatter="dateFormatter" align="center" label="任务结束时间" prop="endTime" width="180" />
      <el-table-column align="center" label="审批建议" prop="reason" min-width="180" />
      <el-table-column align="center" label="耗时" prop="durationInMillis" width="160">
        <template #default="scope">
          {{ formatPast2(scope.row.durationInMillis) }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="流程编号" prop="id" :show-overflow-tooltip="true" />
      <el-table-column align="center" label="任务编号" prop="id" :show-overflow-tooltip="true" />
      <el-table-column align="center" label="操作" fixed="right" width="180">
        <template #default="scope">
          <el-button link type="primary" v-if="scope.row.status === 2" @click="handleReset(scope.row)">重置</el-button>
          <el-button link type="primary" @click="handleAudit(scope.row)">历史</el-button>
          <el-button link type="primary" v-if="scope.row.status === 3" @click="handleDel(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>
</template>
<script lang="ts" setup>
  import { Search, Refresh } from '@element-plus/icons-vue'
  import Pagination from '@/components/common/bpm/Pagination/index.vue'
  import { ElMessageBox } from 'element-plus'

  import { DICT_TYPE } from '@/utils/bpmAdapter/bpmDictAdapter'
  import { dateFormatter, formatPast2 } from '@/utils/bpmAdapter/formatTime'
  import * as ProcessInstanceApi from '@/api/bpm/processInstance'
  import * as TaskApi from '@/api/bpm/task'
  import { useRouter } from 'vue-router'
  import { onMounted, reactive, ref, computed } from 'vue'
  import { useMessage } from '@/components/common/bpm/bpmAdapter/useMessage'
  import { CategoryApi } from '@/api/bpm/category'
  defineOptions({ name: 'BpmTodoTask' })

  const { push } = useRouter() // 路由
  const message = useMessage() // 消息弹窗
  const loading = ref(true) // 列表的加载中
  const total = ref(0) // 列表的总页数
  const list = ref([]) // 列表的数据
  const categoryList = ref<any[]>([]) // 流程分类列表
  const queryParams = reactive({
    pageNo: 1,
    pageSize: 10,
    name: '',
    taskName: '',
    empCode: '',
    deptCode: '',
    businessKey: '',
    processCategory: '',
    createTime: [],
  })
  const queryFormRef = ref() // 搜索的表单

  /** 查询任务列表 */
  const getList = async () => {
    loading.value = true
    try {
      const createTime = queryParams.createTime.join(',')
      const data = await TaskApi.getTaskDonePage({ ...queryParams, createTime })
      list.value = data.list
      total.value = data.total
    } finally {
      loading.value = false
    }
  }

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.pageNo = 1
    getList()
  }

  /** 重置按钮操作 */
  const resetQuery = () => {
    queryFormRef.value.resetFields()
    handleQuery()
  }

  /** 处理审批按钮 */
  const handleAudit = (row: any) => {
    push({
      path: '/bpm/processInstance/detail/index',
      query: {
        id: row.processInstance.id,
      },
    })
  }

  //任务重置
  const handleReset = async row => {
    console.log(row, 'row')
    // 二次确认
    const { value } = await ElMessageBox.prompt('请输入重置原因', '任务重置', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
      inputErrorMessage: '重置原因不能为空',
    })
    if (!value) return
    //发起重置
    await TaskApi.resetTask({ id: row.id, processInstanceId: row.processInstanceId, reason: value })
    message.success('重置成功')
    //刷新列表
    await getList()
  }

  //刪除流程实例
  const handleDel = async row => {
    //二次确认
    await ElMessageBox.confirm('是否确认删除', '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    })
    //发起删除
    await ProcessInstanceApi.deleteProcessInstance(row.processInstanceId)
    message.success('删除成功')
    //刷新列表
    await getList()
  }

  /** 初始化 **/
  onMounted(async () => {
    getList()
    categoryList.value = await CategoryApi.getCategorySimpleList()
  })
</script>
