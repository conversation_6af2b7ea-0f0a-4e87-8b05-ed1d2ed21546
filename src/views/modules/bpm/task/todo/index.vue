<template>
  <!-- <doc-alert title="审批通过、不通过、驳回" url="https://doc.iocoder.cn/bpm/task-todo-done/" /> -->
  <!-- <doc-alert title="审批加签、减签" url="https://doc.iocoder.cn/bpm/sign/" /> -->
  <!-- <doc-alert title="审批转办、委派、抄送" url="https://doc.iocoder.cn/bpm/task-delegation-and-cc/" /> -->
  <!-- <doc-alert title="审批加签、减签" url="https://doc.iocoder.cn/bpm/sign/" /> -->

  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form ref="queryFormRef" :inline="true" :model="queryParams" class="-mb-15px" label-width="68px">
      <el-form-item label="任务名称" prop="name">
        <el-input
          v-model="queryParams.name"
          class="w-240px"
          clearable
          placeholder="请输入任务名称"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="流程分类" prop="category" class="w-240px">
        <el-select v-model="queryParams.processCategory" placeholder="请选择流程分类" clearable>
          <el-option
            v-for="category in categoryList"
            :key="category.code"
            :label="category.name"
            :value="category.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="业务标识" prop="name">
        <el-input
          v-model="queryParams.businessKey"
          class="w-240px"
          clearable
          placeholder="请输入业务标识"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发起人" prop="empCode" class="w-240px">
        <el-select v-model="queryParams.empCode" placeholder="请选择发起人" filterable clearable>
          <el-option v-for="user in userList" :key="user.id" :label="user.empName" :value="user.empCode" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="w-240px"
          end-placeholder="结束日期"
          start-placeholder="开始日期"
          type="daterange"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery" type="primary">
          <Search style="width: 1em; height: 1em; margin-right: 5px"></Search> 搜索
        </el-button>
        <el-button @click="resetQuery">
          <Refresh style="width: 1em; height: 1em; margin-right: 5px"></Refresh> 重置
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list">
      <el-table-column align="center" label="流程" prop="processInstance.name" width="180" />
      <el-table-column align="center" label="业务标识" prop="processInstance.businessKey" width="180" />
      <el-table-column align="center" label="发起人" prop="processInstance.startUser.empName" width="80" />
      <el-table-column align="center" label="发起原因" prop="createReason" width="200" />
      <el-table-column :formatter="dateFormatter" align="center" label="发起时间" prop="createTime" width="120" />
      <el-table-column align="center" label="当前任务" prop="name" width="180" />
      <el-table-column :formatter="dateFormatter" align="center" label="任务时间" prop="createTime" width="180" />
      <el-table-column align="center" label="流程编号" prop="id" :show-overflow-tooltip="true" />
      <el-table-column align="center" label="任务编号" prop="id" :show-overflow-tooltip="true" />
      <el-table-column align="center" label="操作" fixed="right" width="80">
        <template #default="scope">
          <el-button link type="primary" @click="handleAudit(scope.row)">办理</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>
  <!-- 流程详情弹窗 -->

  <ProcessInstanceDetailModal
    :processInstanceId="currentProcessInstanceId"
    v-model:show="processDetailVisible"
    @close="close"
  />
</template>

<script lang="ts" setup>
  import { Icon } from '@/components/common/bpm/Icon'
  import Pagination from '@/components/common/bpm/Pagination/index.vue'
  import { Search, Refresh } from '@element-plus/icons-vue'

  import { dateFormatter } from '@/utils/bpmAdapter/formatTime'
  import * as TaskApi from '@/api/bpm/task'
  import { onMounted, reactive, ref, computed } from 'vue'
  import ProcessInstanceDetailModal from '@/views/modules/bpm/processInstance/detail/processDetailModal.vue'
  import { useRouter } from 'vue-router'
  import { CategoryApi } from '@/api/bpm/category'
  import * as UserApi from '@/api/bpm/bpmAdapter/user.ts'
  import { IPageRes } from '@jtypes'
  import { querySelection } from '@/api/hrm/hrmEmp.ts'
  defineOptions({ name: 'BpmTodoTask' })

  const { push } = useRouter() // 路由

  const loading = ref(true) // 列表的加载中
  const total = ref(0) // 列表的总页数
  const list = ref([]) // 列表的数据
  const categoryList = ref<any[]>([]) // 流程分类列表
  const userList = ref<any[]>([]) // 用户列表
  const queryParams = reactive({
    pageNo: 1,
    pageSize: 10,
    name: '',
    businessKey: '',
    processCategory: '',
    createTime: [],
    empCode: '',
  })
  const queryFormRef = ref() // 搜索的表单

  /** 查询任务列表 */
  const getList = async () => {
    loading.value = true
    try {
      const createTime = queryParams.createTime.join(',')
      const data = await TaskApi.getTaskTodoPage({ ...queryParams, createTime })
      list.value = data.list
      total.value = data.total
    } finally {
      loading.value = false
    }
  }

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.pageNo = 1
    getList()
  }

  /** 重置按钮操作 */
  const resetQuery = () => {
    queryFormRef.value.resetFields()
    handleQuery()
  }
  const currentProcessInstanceId = ref('')
  const processDetailVisible = ref(false)
  const isMobileDevice = inject<Ref<Boolean>>('isMobileDevice')

  /** 处理审批按钮 */
  const handleAudit = (row: any) => {
    if (isMobileDevice.value) {
      push({
        path: '/bpm/processInstance/detail/index',
        query: {
          id: row.processInstance.id,
        },
      })
    } else {
      currentProcessInstanceId.value = row.processInstance.id
      processDetailVisible.value = true
    }
  }

  /** 关闭界面，触发一次查询 **/
  const close = () => {
    queryParams.pageNo = 1
    getList()
  }

  /** 初始化 **/
  onMounted(async () => {
    getList()
    categoryList.value = await CategoryApi.getCategorySimpleList()
    const res: IPageRes = await querySelection({
      pageSize: 2000,
      pageNum: 1,
      empCodeOrEmpName: queryParams.empCode,
      orgId: '',
      hideLoadingBar: true,
      restrictedEmpType: null,
      restrictedEmps: null,
    })
    userList.value = res.data.records
  })
</script>
