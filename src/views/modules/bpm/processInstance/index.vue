<template>
  <!-- <doc-alert title="流程发起、取消、重新发起" url="https://doc.iocoder.cn/bpm/process-instance/" /> -->

  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form class="mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
      <el-form-item label="流程名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入流程名称"
          clearable
          @keyup.enter="handleQuery"
          class="w-240px"
        />
      </el-form-item>
      <el-form-item label="所属流程" prop="processDefinitionId">
        <el-input
          v-model="queryParams.processDefinitionId"
          placeholder="请输入流程定义的编号"
          clearable
          @keyup.enter="handleQuery"
          class="w-240px"
        />
      </el-form-item>
      <el-form-item label="流程分类" prop="category" class="w-240px">
        <el-select v-model="queryParams.category" placeholder="请选择流程分类" clearable>
          <el-option
            v-for="category in categoryList"
            :key="category.code"
            :label="category.name"
            :value="category.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="流程状态" prop="status" class="w-240px">
        <el-select v-model="queryParams.status" placeholder="请选择流程状态" clearable>
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="发起时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery" type="primary">
          <Search style="width: 1em; height: 1em; margin-right: 5px"></Search>
          搜索</el-button
        >
        <el-button @click="resetQuery"
          ><Refresh style="width: 1em; height: 1em; margin-right: 5px"></Refresh>重置</el-button
        >
        <el-button type="primary" plain v-hasPermi="['bpm:process-instance:query']" @click="handleCreate(undefined)">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="icon icon-tabler icon-tabler-plus mr-5px"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            stroke-width="2"
            stroke="currentColor"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path stroke="none" d="M0 0h24v24H0z" fill="none" />
            <line x1="12" y1="5" x2="12" y2="19" />
            <line x1="5" y1="12" x2="19" y2="12" />
          </svg>
          发起流程
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list">
      <el-table-column label="流程名称" align="center" prop="name" min-width="200px" fixed="left" />
      <el-table-column label="业务标识" align="center" prop="businessKey" width="180" fixed="left" />
      <el-table-column label="流程分类" align="center" prop="categoryName" min-width="100" fixed="left" />
      <el-table-column label="流程状态" prop="status" width="120">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="发起时间" align="center" prop="startTime" width="180" :formatter="dateFormatter" />
      <el-table-column label="结束时间" align="center" prop="endTime" width="180" :formatter="dateFormatter" />
      <el-table-column align="center" label="耗时" prop="durationInMillis" width="160">
        <template #default="scope">
          {{ scope.row.durationInMillis > 0 ? formatPast2(scope.row.durationInMillis) : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="当前审批任务" align="center" prop="tasks" min-width="120px">
        <template #default="scope">
          <el-button type="primary" v-for="task in scope.row.tasks" :key="task.id" link>
            <span>{{ task.name }}</span>
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="流程编号" align="center" prop="id" min-width="320px" />
      <el-table-column label="操作" align="center" fixed="right" width="180">
        <template #default="scope">
          <el-button link type="primary" v-hasPermi="['bpm:process-instance:cancel']" @click="handleDetail(scope.row)">
            详情
          </el-button>
          <el-button
            link
            type="primary"
            v-if="scope.row.status === 1"
            v-hasPermi="['bpm:process-instance:query']"
            @click="handleCancel(scope.row)"
          >
            取消
          </el-button>
          <el-button link type="primary" v-else @click="handleCreate(scope.row)"> 重新发起 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>
  <!-- 流程详情弹窗 -->

  <ProcessInstanceDetailModal :processInstanceId="currentProcessInstanceId" v-model:show="processDetailVisible" />
</template>
<script lang="ts" setup>
  import { Icon } from '@/components/common/bpm/Icon'
  import Pagination from '@/components/common/bpm/Pagination/index.vue'
  import { Search, Refresh } from '@element-plus/icons-vue'
  import { getIntDictOptions, DICT_TYPE } from '@/utils/bpmAdapter/bpmDictAdapter'
  import { dateFormatter, formatPast2 } from '@/utils/bpmAdapter/formatTime'
  import { ElMessageBox } from 'element-plus'
  import * as ProcessInstanceApi from '@/api/bpm/processInstance'
  import { CategoryApi } from '@/api/bpm/category'
  import { ProcessInstanceVO } from '@/api/bpm/processInstance'
  import * as DefinitionApi from '@/api/bpm/definition'
  import { onMounted, reactive, ref, computed, onActivated } from 'vue'
  import { useMessage } from '@/components/common/bpm/bpmAdapter/useMessage'
  import { useRouter } from 'vue-router'
  import ProcessInstanceDetailModal from '@/views/modules/bpm/processInstance/detail/processDetailModal.vue'

  defineOptions({ name: 'BpmProcessInstanceMy' })

  const router = useRouter() // 路由
  const message = useMessage() // 消息弹窗
  // const { t } = useI18n() // 国际化
  const t = (a: any) => a

  const processDetailVisible = ref(false)
  const loading = ref(true) // 列表的加载中
  const total = ref(0) // 列表的总页数
  const list = ref([]) // 列表的数据
  const queryParams = reactive({
    pageNo: 1,
    pageSize: 10,
    name: '',
    processDefinitionId: undefined,
    category: undefined,
    status: undefined,
    createTime: [],
  })
  const queryFormRef = ref<any>() // 搜索的表单
  const categoryList = ref<any[]>([]) // 流程分类列表

  /** 查询列表 */
  const getList = async () => {
    loading.value = true
    try {
      const createTime = queryParams.createTime.join(',')
      const data = await ProcessInstanceApi.getProcessInstanceMyPage({ ...queryParams, createTime })
      list.value = data.list
      total.value = data.total
    } finally {
      loading.value = false
    }
  }

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.pageNo = 1
    getList()
  }

  /** 重置按钮操作 */
  const resetQuery = () => {
    queryFormRef.value.resetFields()
    handleQuery()
  }

  /** 发起流程操作 **/
  const handleCreate = async (row?: ProcessInstanceVO) => {
    // 如果是【业务表单】，不支持重新发起
    if (row?.id) {
      const processDefinitionDetail = await DefinitionApi.getProcessDefinition(row.processDefinitionId)
      if (processDefinitionDetail.formType === 20) {
        message.error('重新发起流程失败，原因：该流程使用业务表单，不支持重新发起')
        return
      }
    }
    // 跳转发起流程界面
    await router.push({
      // name: 'BpmProcessInstanceCreate',
      path: '/bpm/processInstance/create/index',
      query: { processInstanceId: row?.id },
    })
  }

  const currentProcessInstanceId = ref('')
  /** 查看详情 */
  const handleDetail = row => {
    currentProcessInstanceId.value = row.id
    processDetailVisible.value = true

    // router.push({
    //   path: '/bpm/processInstance/detail/index',
    //   query: {
    //     id: row.id,
    //   },
    // })
  }

  /** 取消按钮操作 */
  const handleCancel = async row => {
    // 二次确认
    const { value } = await ElMessageBox.prompt('请输入取消原因', '取消流程', {
      confirmButtonText: t('确定'),
      cancelButtonText: t('取消'),
      inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
      inputErrorMessage: '取消原因不能为空',
    })
    // 发起取消
    await ProcessInstanceApi.cancelProcessInstanceByStartUser(row.id, value)
    message.success('取消成功')
    // 刷新列表
    await getList()
  }

  /** 激活时 **/
  onActivated(() => {
    getList()
  })

  /** 初始化 **/
  onMounted(async () => {
    await getList()
    categoryList.value = await CategoryApi.getCategorySimpleList()
  })
</script>
