<template>
  <div class="mobile-bpm-list-page">
    <!-- 搜索栏 -->
    <div class="search-section" v-if="showSearch">
      <div class="search-container">
        <n-input
          v-model:value="quickSearchKeyword"
          placeholder="搜索..."
          clearable
          size="medium"
          class="search-input"
          @keyup.enter="handleQuickSearch"
        >
          <template #prefix>
            <n-icon size="16">
              <search-outline />
            </n-icon>
          </template>
        </n-input>
        <n-button text @click="toggleSearch" class="search-cancel">
          取消
        </n-button>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filter-section" v-if="showFilter">
      <div class="filter-container">
        <n-space vertical :size="12">
          <!-- 动态筛选条件插槽 -->
          <slot name="filters" :queryParams="queryParams" />
        </n-space>
        
        <div class="filter-actions">
          <n-button @click="handleQuery" type="primary" size="medium" block>
            <template #icon>
              <n-icon><search-outline /></n-icon>
            </template>
            搜索
          </n-button>
          <n-button @click="resetQuery" size="medium" block class="mt-2">
            <template #icon>
              <n-icon><refresh-outline /></n-icon>
            </template>
            重置
          </n-button>
        </div>
      </div>
    </div>

    <!-- 顶部操作栏 -->
    <div class="top-actions" v-if="!showSearch && !showFilter">
      <div class="action-buttons">
        <n-button text @click="toggleSearch" class="action-btn">
          <template #icon>
            <n-icon size="20"><search-outline /></n-icon>
          </template>
          搜索
        </n-button>
        <n-button text @click="toggleFilter" class="action-btn">
          <template #icon>
            <n-icon size="20"><filter-outline /></n-icon>
          </template>
          筛选
        </n-button>
        <!-- 额外操作按钮插槽 -->
        <slot name="actions" />
      </div>
    </div>

    <!-- 列表容器 -->
    <div class="list-container">
      <div v-if="loading && list.length === 0" class="loading-container">
        <n-spin size="medium" />
        <div class="loading-text">加载中...</div>
      </div>
      
      <div v-else-if="list.length === 0" class="empty-container">
        <n-empty description="暂无数据" />
      </div>
      
      <div v-else class="item-cards">
        <!-- 卡片内容插槽 -->
        <slot name="card" v-for="(item, index) in list" :key="getItemKey(item, index)" :item="item" :index="index" />
      </div>
      
      <!-- 加载更多 -->
      <div class="load-more-container" v-if="hasMore && !loading">
        <n-button 
          @click="loadMore" 
          :loading="loadingMore"
          size="medium"
          block
          quaternary
        >
          {{ loadingMore ? '加载中...' : '加载更多' }}
        </n-button>
      </div>
      
      <!-- 触底加载指示器 -->
      <div ref="loadTrigger" class="load-trigger" v-if="hasMore"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { 
  SearchOutline, 
  FilterOutline, 
  RefreshOutline 
} from '@vicons/ionicons5'

// Props定义
interface Props {
  // 列表数据
  list: any[]
  // 总数
  total: number
  // 加载状态
  loading: boolean
  // 查询参数
  queryParams: Record<string, any>
  // 获取项目唯一键的函数
  getItemKey?: (item: any, index: number) => string | number
  // 是否启用触底加载
  enableInfiniteScroll?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  list: () => [],
  total: 0,
  loading: false,
  queryParams: () => ({}),
  getItemKey: (item, index) => item.id || index,
  enableInfiniteScroll: true
})

// Emits定义
const emit = defineEmits<{
  query: []
  reset: []
  loadMore: []
  quickSearch: [keyword: string]
}>()

// 响应式数据
const showSearch = ref(false)
const showFilter = ref(false)
const quickSearchKeyword = ref('')
const loadingMore = ref(false)
const loadTrigger = ref<HTMLElement>()

// 计算属性
const hasMore = computed(() => props.list.length < props.total)

// 方法
const toggleSearch = () => {
  showSearch.value = !showSearch.value
  showFilter.value = false
  if (!showSearch.value) {
    quickSearchKeyword.value = ''
  }
}

const toggleFilter = () => {
  showFilter.value = !showFilter.value
  showSearch.value = false
}

const handleQuickSearch = () => {
  emit('quickSearch', quickSearchKeyword.value)
}

const handleQuery = () => {
  showFilter.value = false
  emit('query')
}

const resetQuery = () => {
  emit('reset')
}

const loadMore = () => {
  if (!hasMore.value || loadingMore.value) return
  loadingMore.value = true
  emit('loadMore')
}

// 触底加载
let observer: IntersectionObserver | null = null

const setupInfiniteScroll = () => {
  if (!props.enableInfiniteScroll || !loadTrigger.value) return
  
  observer = new IntersectionObserver(
    (entries) => {
      const entry = entries[0]
      if (entry.isIntersecting && hasMore.value && !props.loading && !loadingMore.value) {
        loadMore()
      }
    },
    {
      rootMargin: '100px'
    }
  )
  
  observer.observe(loadTrigger.value)
}

const cleanupInfiniteScroll = () => {
  if (observer) {
    observer.disconnect()
    observer = null
  }
}

// 监听loadingMore变化
const stopLoadingMore = () => {
  loadingMore.value = false
}

// 暴露方法给父组件
defineExpose({
  stopLoadingMore
})

onMounted(() => {
  nextTick(() => {
    setupInfiniteScroll()
  })
})

onUnmounted(() => {
  cleanupInfiniteScroll()
})
</script>

<style scoped>
@reference "tailwindcss";

.mobile-bpm-list-page {
  @apply min-h-screen bg-gray-50 flex flex-col;
}

/* 搜索栏样式 */
.search-section {
  @apply bg-white border-b border-gray-200 px-4 py-3 sticky top-0 z-10;
}

.search-container {
  @apply flex items-center gap-3;
}

.search-input {
  @apply flex-1;
}

.search-cancel {
  @apply text-blue-500 font-medium;
}

/* 筛选器样式 */
.filter-section {
  @apply bg-white border-b border-gray-200 px-4 py-4 sticky top-0 z-10;
}

.filter-actions {
  @apply mt-4;
}

/* 顶部操作栏 */
.top-actions {
  @apply bg-white border-b border-gray-200 px-4 py-3 sticky top-0 z-10;
}

.action-buttons {
  @apply flex items-center justify-between;
}

.action-btn {
  @apply p-2 rounded-full hover:bg-gray-100 transition-colors;
}

/* 列表容器 */
.list-container {
  @apply flex-1 px-4 py-4;
}

.loading-container {
  @apply flex flex-col items-center justify-center py-12;
}

.loading-text {
  @apply mt-2 text-gray-500 text-sm;
}

.empty-container {
  @apply py-12;
}

/* 卡片列表 */
.item-cards {
  @apply space-y-3;
}

/* 加载更多 */
.load-more-container {
  @apply mt-4;
}

.load-trigger {
  @apply h-1;
}

/* 响应式优化 */
@media (max-width: 480px) {
  .filter-section {
    @apply px-3 py-3;
  }
  
  .list-container {
    @apply px-3 py-3;
  }
}
</style>
