package com.jp.med.ams.modules.property.service.read.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.ams.modules.changes.dto.AmsChgRcdDto;
import com.jp.med.ams.modules.changes.mapper.read.AmsChgRcdReadMapper;
import com.jp.med.ams.modules.changes.mapper.read.AmsTransferReadMapper;
import com.jp.med.ams.modules.changes.vo.AmsChgRcdVo;
import com.jp.med.ams.modules.property.dto.AmsInfoChangesDto;
import com.jp.med.ams.modules.property.dto.AmsPropertyDto;
import com.jp.med.ams.modules.property.dto.AmsScrapDto;
import com.jp.med.ams.modules.property.mapper.read.AmsInfoChangesReadMapper;
import com.jp.med.ams.modules.property.mapper.read.AmsPropertyReadMapper;
import com.jp.med.ams.modules.property.mapper.read.AmsScrapApplyReadMapper;
import com.jp.med.ams.modules.property.service.read.AmsPropertyReadService;
import com.jp.med.ams.modules.property.vo.AmsInfoChangesVo;
import com.jp.med.ams.modules.property.vo.AmsPropertyVo;
import com.jp.med.ams.modules.property.vo.AmsRecordsVo;
import com.jp.med.ams.modules.property.vo.AmsScrapVo;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.dto.common.CommonExtendFormDto;
import com.jp.med.common.entity.sys.SysRole;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.OSSUtil;
import com.jp.med.common.vo.SelectOptionVo;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 资产管理系统-资产查询服务实现类
 * 该类负责实现所有与资产查询相关的业务逻辑
 * 使用Spring的事务管理，并设置为只读模式，提高查询性能
 */
@Transactional(readOnly = true)
@Service
public class AmsPropertyReadServiceImpl extends ServiceImpl<AmsPropertyReadMapper, AmsPropertyDto>
        implements AmsPropertyReadService {

    /**
     * 资产查询Mapper接口，用于数据库交互
     */
    @Autowired
    private AmsPropertyReadMapper amsPropertyReadMapper;

    /**
     * 资产变更记录查询Mapper接口
     */
    @Autowired
    private AmsChgRcdReadMapper amsChgRcdReadMapper;

    /**
     * 资产信息变更查询Mapper接口
     */
    @Autowired
    private AmsInfoChangesReadMapper amsInfoChangesReadMapper;

    /**
     * 资产转移查询Mapper接口
     */
    @Autowired
    private AmsTransferReadMapper amsTransferReadMapper;

    /**
     * 资产报废查询服务实现类
     */
    @Autowired
    private AmsScrapReadServiceImpl amsScrapReadServiceImpl;

    /**
     * 资产报废申请查询Mapper接口
     */
    @Autowired
    private AmsScrapApplyReadMapper amsScrapApplyReadMapper;

    /**
     * 查询资产列表
     * 根据传入的查询条件，查询符合条件的资产列表
     *
     * @param dto 查询参数对象，包含分页信息和查询条件
     * @return 资产列表视图对象
     */
    @Override
    public List<AmsPropertyVo> queryList(AmsPropertyDto dto) {

        // 处理分页参数，如果传入了分页参数则进行分页查询
        if (dto.getPageNum() != null && dto.getPageSize() != null) {
            PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        }

        // 处理扩展表单参数，用于复杂查询条件的构建
        List<CommonExtendFormDto> extendForm = dto.getExtendForm();
        List<CommonExtendFormDto> extendFormNew = new ArrayList<>();
        if (!Objects.isNull(extendForm)) {
            for (CommonExtendFormDto formDto : extendForm) {
                // 如果搜索数据为空，设置为空字符串避免空指针异常
                if (Objects.isNull(formDto.getSearchData())) {
                    formDto.setSearchData("");
                }
                CommonExtendFormDto commonExtendFormDto = new CommonExtendFormDto();
                // 确保关键字不为空，且只处理字母组成的关键字（安全过滤）
                if (!Objects.isNull(formDto.getRealKey())) {
                    if (formDto.getRealKey().matches("[a-zA-Z_]+")) {
                        commonExtendFormDto.setKey(formDto.getRealKey());
                        commonExtendFormDto.setSearchType(formDto.getSearchType());
                        // 根据搜索数据类型，设置不同的数据字段
                        if (formDto.getSearchData() instanceof ArrayList) {
                            // 列表类型数据
                            commonExtendFormDto.setListData((ArrayList<Object>) formDto.getSearchData());
                            commonExtendFormDto.setDataType(MedConst.TYPE_1);
                        } else if (formDto.getSearchData() instanceof Integer) {
                            // 数字类型数据
                            commonExtendFormDto.setNumData(Integer.parseInt(String.valueOf(formDto.getSearchData())));
                            commonExtendFormDto.setDataType(MedConst.TYPE_2);
                        } else if (formDto.getSearchData() instanceof String) {
                            // 字符串类型数据
                            commonExtendFormDto.setStringData(String.valueOf(formDto.getSearchData()));
                            commonExtendFormDto.setDataType(MedConst.TYPE_3);
                        }
                        extendFormNew.add(commonExtendFormDto);
                    }
                }
            }
        }
        // 更新处理后的扩展表单数据
        dto.setExtendForm(extendFormNew);

        // 从extendFormNew获取 查询的资产类型，资产新类型，使用科室，管理科室 赋值到dto中 同时 删除
        // 用于复用xml sql 中的cte递归查询下级选项
        List<CommonExtendFormDto> toRemove = new ArrayList<>();
        for (CommonExtendFormDto formDto : extendFormNew) {
            String key = formDto.getKey();
            if (key != null) {
                // 定义需要处理的字段及其对应的setter方法
                Map<String, Consumer<String>> fieldHandlers = new HashMap<>();

                fieldHandlers.put("asset_type", dto::setAssetType);
                fieldHandlers.put("asset_type_n", dto::setAssetTypeN);
                fieldHandlers.put("dept_use", dto::setDeptUse);
                fieldHandlers.put("dept", dto::setDept);

                if (fieldHandlers.containsKey(key)) {
                    // 统一处理字符串和列表数据
                    String value = null;
                    if (formDto.getStringData() != null && !formDto.getStringData().isEmpty()) {
                        value = formDto.getStringData();
                    } else if (formDto.getListData() != null && !formDto.getListData().isEmpty()) {
                        value = String.valueOf(formDto.getListData().get(0));
                    }

                    if (value != null) {
                        fieldHandlers.get(key).accept(value);
                        toRemove.add(formDto);
                    }
                }
            }
        }
        // 从extendFormNew中移除已处理的字段
        extendFormNew.removeAll(toRemove);

        // 如果请求汇总数据，则调用汇总查询接口
        if (StrUtil.equals(dto.getSummary(), "1")) {
            List<AmsPropertyVo> amsPropertyVos = amsPropertyReadMapper.queryListSum(dto);
            // 检查列表是否为空
            if (amsPropertyVos != null && !amsPropertyVos.isEmpty()) {
                AmsPropertyVo amsPropertyVo = amsPropertyVos.get(0);
                
                // 计算净值：原值减去折旧，处理null值情况
                // 处理NR相关数据
                BigDecimal nrAssetNav = amsPropertyVo.getTotalNRassetNav();
                BigDecimal nrDep = amsPropertyVo.getTotalNRdep();
                if (nrAssetNav != null && nrDep != null) {
                    amsPropertyVo.setTotalNRnbv(nrAssetNav.subtract(nrDep));
                } else {
                    // 如果任一值为null，设置净值为0或相应的非null值
                    amsPropertyVo.setTotalNRnbv(nrAssetNav != null ? nrAssetNav : (nrDep != null ? nrDep.negate() : BigDecimal.ZERO));
                }
                
                // 处理AL相关数据
                BigDecimal alAssetNav = amsPropertyVo.getTotalALassetNav();
                BigDecimal alDep = amsPropertyVo.getTotalALdep();
                if (alAssetNav != null && alDep != null) {
                    amsPropertyVo.setTotalALnbv(alAssetNav.subtract(alDep));
                } else {
                    // 如果任一值为null，设置净值为0或相应的非null值
                    amsPropertyVo.setTotalALnbv(alAssetNav != null ? alAssetNav : (alDep != null ? alDep.negate() : BigDecimal.ZERO));
                }
            }
            return amsPropertyVos;
        }
        // 默认调用普通列表查询接口
        return amsPropertyReadMapper.queryList(dto);
    }

    /**
     * 查询资产汇总列表
     * 该方法已被注释，但保留代码，可能供将来使用
     *
     * @param dto 查询参数对象
     * @return 资产汇总列表
     */
    // @Override
    public List<AmsPropertyVo> queryListSum(AmsPropertyDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return amsPropertyReadMapper.queryListSum(dto);
    }

    /**
     * 查询资产记录
     * 获取与特定资产相关的所有记录信息
     *
     * @param dto 查询参数对象
     * @return 资产记录列表
     */
    @Override
    public List<AmsRecordsVo> queryRecords(AmsPropertyDto dto) {
        // 设置SQL自动化医院条件，确保查询范围正确
        dto.setSqlAutowiredHospitalCondition(true);
        List<AmsRecordsVo> recordsVos = amsPropertyReadMapper.queryRecords(dto);
        if (!recordsVos.isEmpty()) {
            for (AmsRecordsVo recordsVo : recordsVos) {
                String attachment = recordsVo.getAttachment();
                if (StringUtils.isEmpty(attachment)) {
                    break;
                }
                // 通过OSS工具获取附件的预签名URL
                String url = OSSUtil.getPresignedObjectUrl(OSSConst.BUCKET_AMS, attachment);
                recordsVo.setUrl(url);
            }
        }
        return recordsVos;
    }

    /**
     * 查询使用部门列表
     * 获取所有正在使用资产的部门信息
     *
     * @param dto 查询参数对象
     * @return 部门下拉选项列表
     */
    @Override
    public List<SelectOptionVo> queryUseDept(AmsPropertyDto dto) {
        return amsPropertyReadMapper.queryUseDept(dto);
    }

    /**
     * 查询资产主列表
     * 获取资产的主要信息列表，同时考虑用户权限
     *
     * @param dto 查询参数对象
     * @return 资产主列表视图对象
     */
    @Override
    public List<AmsPropertyVo> queryMainList(AmsPropertyDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        // 获取资产详情路由权限
        List<SysRole> permissionList = dto.getSysUser().getPermissionList();
        // 如果用户没有管理员权限，则只能查看自己部门的资产
        if (permissionList.stream().map(SysRole::getName).noneMatch(name -> name.contains("AMS-ADMIN"))) {
            if (!StrUtil.equals(dto.getSysUser().getUsername(), "admin")) {
                dto.setCurSysOrgId(dto.getSysUser().getHrmUser().getHrmOrgId());
            }
        }
        return amsPropertyReadMapper.queryMainList(dto);
    }

    /**
     * 根据资产编码查询资产详情
     *
     * @param dto 包含资产编码的查询参数对象
     * @return 资产详情视图对象
     * @throws AppException 如果资产信息查询失败
     */
    @Override
    public AmsPropertyVo queryByCode(AmsPropertyDto dto) {
        String faCode = dto.getFaCode();

        // 查询主列表信息
        List<AmsPropertyVo> amsPropertyVos = amsPropertyReadMapper.queryMainList(dto);
        if (amsPropertyVos.size() != 1) {
            throw new AppException("资产信息查询失败");
        }
        AmsPropertyVo amsPropertyVo = amsPropertyVos.get(0);
        // 查询资产相关记录并设置到资产视图对象中
        List<AmsRecordsVo> recordsVos = queryRecords(dto);
        amsPropertyVo.setRecordsVos(recordsVos);
        return amsPropertyVo;
    }

    /**
     * 查询资产变更历史
     * 获取特定资产的所有变更记录
     *
     * @param dto 查询参数对象
     * @return 变更记录列表
     */
    @Override
    public List<AmsChgRcdVo> queryChgHistory(AmsPropertyDto dto) {
        // 查询变更记录
        AmsChgRcdDto chgRcdDto = new AmsChgRcdDto();
        chgRcdDto.setFaCode(dto.getFaCode());
        List<AmsChgRcdVo> amsChgRcdVos = amsChgRcdReadMapper.queryList(chgRcdDto);

        // 查询信息变更记录
        AmsInfoChangesDto changesDto = new AmsInfoChangesDto();
        changesDto.setFaCode(dto.getFaCode());
        changesDto.setChgCode(StrUtil.toUnderlineCase(changesDto.getChgCode()));

        List<AmsInfoChangesVo> amsInfoChangesVos = amsInfoChangesReadMapper.queryList(changesDto);
        // 将信息变更记录转换为变更记录格式并合并
        for (AmsInfoChangesVo amsInfoChangesVo : amsInfoChangesVos) {
            AmsChgRcdVo amsChgRcdVo = new AmsChgRcdVo();
            BeanUtils.copyProperties(amsInfoChangesVo, amsChgRcdVo);
            amsChgRcdVo.setChgType(amsInfoChangesVo.getChgName());
            amsChgRcdVos.add(amsChgRcdVo);
        }
        return amsChgRcdVos;
    }

    /**
     * 分页查询资产变更历史
     * 按页获取资产变更记录，用于前端分页展示
     *
     * @param dto 包含分页信息的查询参数对象
     * @return 分页的变更记录列表
     */
    @Override
    public List<AmsChgRcdVo> queryChgHistoryPage(AmsPropertyDto dto) {
        AmsChgRcdDto chgRcdDto = new AmsChgRcdDto();
        chgRcdDto.setFaCode(dto.getFaCode());
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        // 使用聚合接口查询变更记录
        List<AmsChgRcdVo> amsChgRcdVos = amsChgRcdReadMapper.queryList2Polymerization(chgRcdDto);

        // 以下代码已注释，可能是旧的查询方式
        // PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        // AmsChgRcdDto chgRcdDto = new AmsChgRcdDto();
        // chgRcdDto.setFaCode(dto.getFaCode());
        // List<AmsChgRcdVo> amsChgRcdVos = amsChgRcdReadMapper.queryList(chgRcdDto);
        // AmsInfoChangesDto changesDto = new AmsInfoChangesDto();
        // changesDto.setFaCode(dto.getFaCode());
        // changesDto.setChgCode(StrUtil.toUnderlineCase(changesDto.getChgCode()));
        // List<AmsInfoChangesVo> amsInfoChangesVos =
        // amsInfoChangesReadMapper.queryList(changesDto);
        //
        // for (AmsInfoChangesVo amsInfoChangesVo : amsInfoChangesVos) {
        // AmsChgRcdVo amsChgRcdVo = new AmsChgRcdVo();
        // BeanUtils.copyProperties(amsInfoChangesVo, amsChgRcdVo);
        // amsChgRcdVos.add(amsChgRcdVo);
        // }
        return amsChgRcdVos;
    }

    /**
     * 查询不在转移和分配流程中的资产
     * 用于过滤出可进行操作的资产列表
     *
     * @param dto 查询参数对象
     * @return 可操作的资产列表
     */
    @Override
    public List<AmsPropertyVo> queryNotInTransferAndAllocProperty(AmsPropertyDto dto) {
        // 查询当前在转移和分配流程中的资产列表
        List<AmsPropertyDto> amsPropertyDtos = amsTransferReadMapper.queryTransferAndAllocProcessProperty();
        // 提取需要排除的资产编码列表
        List<String> excludeFaCodes = amsPropertyDtos.stream().map(AmsPropertyDto::getFaCode)
                .collect(Collectors.toList());

        AmsPropertyDto queryDto = new AmsPropertyDto();
        BeanUtils.copyProperties(dto, queryDto);
        queryDto.setExcludeFaCodes(excludeFaCodes);
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());

        // 设置查询条件：已审核、未取消、类型为1
        queryDto.setIsChk("1");
        queryDto.setIsCanc("0");
        queryDto.setType("1");
        return amsPropertyReadMapper.queryMainList(queryDto);
    }

    /**
     * 查询未报废的资产
     * 排除已经在报废流程中的资产
     *
     * @param dto 查询参数对象
     * @return 未报废的资产列表
     */
    @Override
    public List<AmsPropertyVo> queryAllApplyScrapAssets(AmsPropertyDto dto) {
        var queryDto = new AmsScrapDto();

        // 查询年度汇总报废资产列表
        List<AmsScrapVo> list = amsScrapReadServiceImpl.queryAnnualSummary(queryDto);
        // 提取需要排除的资产编码
        List<String> excludeFaCodes = list.stream().map(AmsScrapVo::getFaCode).collect(Collectors.toList());
        // 以下代码已注释，可能是另一种查询方式
        // 排查全部发起过流程的资产
        // List<String> applyFaCodes = amsScrapApplyReadMapper.queryAllApplyAssets();
        // List<String> excludeFaCodes = new ArrayList<>(applyFaCodes);

        dto.setExcludeFaCodes(excludeFaCodes);

        // 同时排除在转移和分配流程中的资产
        List<AmsPropertyDto> amsPropertyDtos = amsTransferReadMapper.queryTransferAndAllocProcessProperty();
        List<String> excludeFaCodes2 = amsPropertyDtos.stream().map(AmsPropertyDto::getFaCode)
                .collect(Collectors.toList());
        dto.getExcludeFaCodes().addAll(excludeFaCodes2);
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());

        // 设置查询条件：已审核、未取消、类型为1
        dto.setIsChk("1");
        dto.setIsCanc("0");
        dto.setType("1");
        return amsPropertyReadMapper.queryMainList(dto);
    }

    /**
     * 平均年龄折旧法计算
     * 目前为空实现，可能是待开发的功能
     *
     * @param amsPropertyDto 资产参数对象
     */
    @Override
    public void averageAgeDepreciation(AmsPropertyDto amsPropertyDto) {
        // 空实现，可能是待开发的功能
    }

    /**
     * 查询子资产列表
     * 获取特定资产的子资产信息
     *
     * @param dto 查询参数对象
     * @return 子资产列表
     */
    @Override
    public List<AmsPropertyVo> querySon(AmsPropertyDto dto) {
        // 设置SQL自动化医院条件
        dto.setSqlAutowiredHospitalCondition(true);
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return amsPropertyReadMapper.querySon(dto);
    }

    /**
     * 查询资产按分类的数量汇总
     * 统计各个分类下的资产数量
     *
     * @param dto 查询参数对象
     * @return 分类资产数量统计结果
     */
    @Override
    public Object countByCategory(AmsPropertyDto dto) {
        // 构建基础查询条件
        AmsPropertyDto queryDto = new AmsPropertyDto();
        queryDto.setType("1");
        queryDto.setIsCanc("0");
        queryDto.setIsChk("1");
        queryDto.setSysUser(dto.getSysUser());
        queryDto.setOnlyQueryId(true);
        queryDto.setHospitalId(dto.getHospitalId());

        // 查询所有符合条件的资产ID列表
        List<AmsPropertyVo> amsPropertyVos = queryList(queryDto);
        List<Integer> idList = amsPropertyVos.stream().map(AmsPropertyVo::getId).collect(Collectors.toList());

        // 根据ID列表统计各分类资产数量
        return amsPropertyReadMapper.countByCategory(idList);
    }

    /**
     * 查询资产按分类的原值汇总
     * 统计各个分类下资产的原值总和
     *
     * @param dto 查询参数对象
     * @return 分类资产原值汇总结果
     */
    @Override
    public Object sumByCategory(AmsPropertyDto dto) {
        // 构建基础查询条件
        AmsPropertyDto queryDto = new AmsPropertyDto();
        queryDto.setType("1");
        queryDto.setIsCanc("0");
        queryDto.setIsChk("1");
        queryDto.setSysUser(dto.getSysUser());
        queryDto.setOnlyQueryId(true);
        queryDto.setHospitalId(dto.getHospitalId());

        // 查询所有符合条件的资产ID列表
        List<AmsPropertyVo> amsPropertyVos = queryList(queryDto);
        List<Integer> idList = amsPropertyVos.stream().map(AmsPropertyVo::getId).collect(Collectors.toList());

        // 根据ID列表汇总各分类资产原值
        return amsPropertyReadMapper.sumByCategory(idList);
    }

    /**
     * 查询资产科室分布
     * 获取资产在各个科室的分布情况，按数量排序
     *
     * @param dto 查询参数对象
     * @return 科室资产分布统计结果
     */
    @Override
    public Object topCountByDept(AmsPropertyDto dto) {
        // 构建基础查询条件
        AmsPropertyDto queryDto = new AmsPropertyDto();
        queryDto.setType("1");
        queryDto.setIsCanc("0");
        queryDto.setIsChk("1");
        queryDto.setSysUser(dto.getSysUser());
        queryDto.setOnlyQueryId(true);
        queryDto.setHospitalId(dto.getHospitalId());

        // 查询所有符合条件的资产ID列表
        List<AmsPropertyVo> amsPropertyVos = getBaseMapper().queryList(queryDto);
        List<Integer> idList = amsPropertyVos.stream().map(AmsPropertyVo::getId).collect(Collectors.toList());

        // 根据ID列表统计各科室资产数量并排序
        return amsPropertyReadMapper.topCountByDept(idList);
    }
}
